#!/bin/bash

echo "🚀 Starting Open WebUI Development Services..."
echo

# Check if we're in the right directory
if [ ! -f "package.json" ]; then
    echo "❌ Error: package.json not found. Please run this script from the project root."
    exit 1
fi

if [ ! -f "backend/dev.sh" ]; then
    echo "❌ Error: backend/dev.sh not found."
    exit 1
fi

echo "📁 Current directory: $(pwd)"
echo

# Function to start frontend
start_frontend() {
    echo "🌐 Starting Frontend (Vite)..."
    npm run dev
}

# Function to start backend
start_backend() {
    echo "🔧 Starting Backend (FastAPI)..."
    cd backend
    bash dev.sh
}

# Check if argument is provided
if [ "$1" = "frontend" ]; then
    start_frontend
elif [ "$1" = "backend" ]; then
    start_backend
elif [ "$1" = "both" ]; then
    echo "⚠️  Starting both services requires two terminals."
    echo "   Frontend: npm run dev"
    echo "   Backend:  cd backend && bash dev.sh"
    echo
    echo "Choose one to start now:"
    echo "  ./start-dev.sh frontend"
    echo "  ./start-dev.sh backend"
else
    echo "Usage: $0 [frontend|backend|both]"
    echo
    echo "Available options:"
    echo "  frontend  - Start the frontend development server (port 5173)"
    echo "  backend   - Start the backend development server (port 8080)"
    echo "  both      - Show instructions for starting both"
    echo
    echo "Examples:"
    echo "  ./start-dev.sh frontend"
    echo "  ./start-dev.sh backend"
    echo
    echo "After starting services, access:"
    echo "  Frontend: http://localhost:5173"
    echo "  Backend:  http://localhost:8080"
fi
