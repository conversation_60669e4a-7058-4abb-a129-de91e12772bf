#!/bin/bash

echo "🚀 Starting Open WebUI services via Docker exec..."
echo

CONTAINER_ID="a6e9e651cceee2e1b605981bd47872fafba5e41a4dda0cc649b4bd42b6f9d6b1"

# Kill any existing processes
echo "🧹 Cleaning up any existing processes..."
docker exec $CONTAINER_ID pkill -f "vite\|uvicorn\|npm" 2>/dev/null || true

# Start frontend in background
echo "🌐 Starting Frontend (Vite)..."
docker exec -d $CONTAINER_ID bash -c "cd /workspaces/open-webui && npx vite dev --host 0.0.0.0 --port 5173"

# Start backend in background
echo "🔧 Starting Backend (FastAPI)..."
docker exec -d $CONTAINER_ID bash -c "cd /workspaces/open-webui/backend && python3 -m uvicorn open_webui.main:app --host 0.0.0.0 --port 8080 --reload"

echo
echo "⏳ Waiting for services to start..."
sleep 5

echo
echo "🔍 Checking service status..."

# Check frontend
FRONTEND_STATUS=$(curl -s -o /dev/null -w "%{http_code}" http://localhost:5173)
if [ "$FRONTEND_STATUS" = "200" ]; then
    echo "✅ Frontend: http://localhost:5173 (Status: $FRONTEND_STATUS)"
else
    echo "⚠️  Frontend: http://localhost:5173 (Status: $FRONTEND_STATUS)"
fi

# Check backend
BACKEND_STATUS=$(curl -s -o /dev/null -w "%{http_code}" http://localhost:8080)
if [ "$BACKEND_STATUS" = "200" ] || [ "$BACKEND_STATUS" = "404" ]; then
    echo "✅ Backend: http://localhost:8080 (Status: $BACKEND_STATUS)"
else
    echo "⚠️  Backend: http://localhost:8080 (Status: $BACKEND_STATUS)"
fi

echo
echo "📊 Process status:"
docker exec $CONTAINER_ID ps aux | grep -E "(vite|uvicorn|node)" | head -5

echo
echo "🔌 Port status:"
docker exec $CONTAINER_ID netstat -tlpn | grep -E "(5173|8080)"

echo
echo "🎉 Services should be starting up!"
echo "   Frontend: http://localhost:5173"
echo "   Backend:  http://localhost:8080"
echo
echo "🔍 To check logs:"
echo "   docker exec -it $CONTAINER_ID bash"
echo "   then run: ps aux | grep -E \"(vite|uvicorn)\""
