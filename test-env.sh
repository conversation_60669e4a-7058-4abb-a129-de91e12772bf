#!/bin/bash

echo "🔍 Testing Open WebUI Development Environment..."
echo

# Check if we're in the right directory
echo "📁 Current directory: $(pwd)"
echo

# Check Node.js
echo "🟢 Node.js version:"
node --version
echo

# Check npm
echo "🟢 npm version:"
npm --version
echo

# Check Python
echo "🟢 Python version:"
python3 --version
echo

# Check if node_modules exists
if [ -d "node_modules" ]; then
    echo "✅ node_modules directory exists"
else
    echo "❌ node_modules directory missing"
fi

# Check if backend directory exists
if [ -d "backend" ]; then
    echo "✅ backend directory exists"
else
    echo "❌ backend directory missing"
fi

# Check if package.json exists
if [ -f "package.json" ]; then
    echo "✅ package.json exists"
else
    echo "❌ package.json missing"
fi

# Check if backend requirements.txt exists
if [ -f "backend/requirements.txt" ]; then
    echo "✅ backend/requirements.txt exists"
else
    echo "❌ backend/requirements.txt missing"
fi

echo
echo "🚀 Ready to start development!"
echo
echo "To start the services:"
echo "  Frontend: npm run dev"
echo "  Backend:  cd backend && bash dev.sh"
echo
echo "URLs will be:"
echo "  Frontend: http://localhost:5173"
echo "  Backend:  http://localhost:8080"
