{"-1 for no limit, or a positive integer for a specific limit": "", "'s', 'm', 'h', 'd', 'w' or '-1' for no expiration.": "'s', 'm', 'h', 'd', 'w' ou '-1' para nenhuma expiração.", "(e.g. `sh webui.sh --api --api-auth username_password`)": "", "(e.g. `sh webui.sh --api`)": "(por exemplo, `sh webui.sh --api`)", "(latest)": "(mais recente)", "(leave blank for to use commercial endpoint)": "", "[Last] dddd [at] h:mm A": "", "[Today at] h:mm A": "", "[Yesterday at] h:mm A": "", "{{ models }}": "{{ modelos }}", "{{COUNT}} Available Tools": "", "{{COUNT}} characters": "", "{{COUNT}} hidden lines": "", "{{COUNT}} Replies": "", "{{COUNT}} words": "", "{{user}}'s Chats": "{{user}}'s Chats", "{{webUIName}} Backend Required": "{{webUIName}} Backend Necessário", "*Prompt node ID(s) are required for image generation": "", "A new version (v{{LATEST_VERSION}}) is now available.": "", "A task model is used when performing tasks such as generating titles for chats and web search queries": "Um modelo de tarefa é usado ao executar tarefas como gerar títulos para bate-papos e consultas de pesquisa na Web", "a user": "um utilizador", "About": "Acerca de", "Accept autocomplete generation / Jump to prompt variable": "", "Access": "", "Access Control": "", "Accessible to all users": "", "Account": "Conta", "Account Activation Pending": "Ativação da Conta Pendente", "Accurate information": "Informações precisas", "Action": "", "Actions": "", "Activate": "", "Activate this command by typing \"/{{COMMAND}}\" to chat input.": "", "Active Users": "Utilizadores Ativos", "Add": "<PERSON><PERSON><PERSON><PERSON>", "Add a model ID": "", "Add a short description about what this model does": "Adicione uma breve descrição sobre o que este modelo faz", "Add a tag": "Adici<PERSON>r uma tag", "Add Arena Model": "", "Add Connection": "", "Add Content": "", "Add content here": "", "Add Custom Parameter": "", "Add custom prompt": "Adicionar um prompt curto", "Add Files": "<PERSON><PERSON><PERSON><PERSON>", "Add Group": "", "Add Memory": "Adicionar <PERSON>", "Add Model": "Adicionar <PERSON>o", "Add Reaction": "", "Add Tag": "", "Add Tags": "adicionar tags", "Add text content": "", "Add User": "<PERSON><PERSON><PERSON><PERSON>", "Add User Group": "", "Adjusting these settings will apply changes universally to all users.": "Ajustar essas configurações aplicará alterações universalmente a todos os utilizadores.", "admin": "administrador", "Admin": "Admin", "Admin Panel": "Painel do Administrador", "Admin Settings": "Configurações do Administrador", "Admins have access to all tools at all times; users need tools assigned per model in the workspace.": "", "Advanced Parameters": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Advanced Params": "Params Avançados", "AI": "", "All": "", "All Documents": "Todos os Documentos", "All models deleted successfully": "", "Allow Call": "", "Allow Chat Controls": "", "Allow Chat Delete": "", "Allow Chat Deletion": "Permitir <PERSON> de Conversa", "Allow Chat Edit": "", "Allow Chat Export": "", "Allow Chat Share": "", "Allow Chat System Prompt": "", "Allow File Upload": "", "Allow Multiple Models in Chat": "", "Allow non-local voices": "<PERSON><PERSON><PERSON> vozes não locais", "Allow Speech to Text": "", "Allow Temporary Chat": "", "Allow Text to Speech": "", "Allow User Location": "", "Allow Voice Interruption in Call": "", "Allowed Endpoints": "", "Allowed File Extensions": "", "Allowed file extensions for upload. Separate multiple extensions with commas. Leave empty for all file types.": "", "Already have an account?": "Já tem uma conta?", "Alternative to the top_p, and aims to ensure a balance of quality and variety. The parameter p represents the minimum probability for a token to be considered, relative to the probability of the most likely token. For example, with p=0.05 and the most likely token having a probability of 0.9, logits with a value less than 0.045 are filtered out.": "", "Always": "", "Always Collapse Code Blocks": "", "Always Expand Details": "", "Always Play Notification Sound": "", "Amazing": "", "an assistant": "um assistente", "Analyzed": "", "Analyzing...": "", "and": "e", "and {{COUNT}} more": "", "and create a new shared link.": "e criar um novo link partilhado.", "Android": "", "API": "", "API Base URL": "URL Base da API", "API details for using a vision-language model in the picture description. This parameter is mutually exclusive with picture_description_local.": "", "API Key": "<PERSON><PERSON> da <PERSON>", "API Key created.": "<PERSON>ve da <PERSON> criada.", "API Key Endpoint Restrictions": "", "API keys": "<PERSON><PERSON> da <PERSON>", "API Version": "", "Application DN": "", "Application DN Password": "", "applies to all users with the \"user\" role": "", "April": "Abril", "Archive": "Arquivo", "Archive All Chats": "<PERSON><PERSON><PERSON><PERSON> todos os chats", "Archived Chats": "Conversas arquivadas", "archived-chat-export": "", "Are you sure you want to clear all memories? This action cannot be undone.": "", "Are you sure you want to delete this channel?": "", "Are you sure you want to delete this message?": "", "Are you sure you want to unarchive all archived chats?": "", "Are you sure?": "Tem a certeza?", "Arena Models": "", "Artifacts": "", "Ask": "", "Ask a question": "", "Assistant": "", "Attach file from knowledge": "", "Attention to detail": "<PERSON><PERSON><PERSON><PERSON>", "Attribute for Mail": "", "Attribute for Username": "", "Audio": "<PERSON><PERSON><PERSON>", "August": "Agosto", "Auth": "", "Authenticate": "", "Authentication": "", "Auto": "", "Auto-Copy Response to Clipboard": "Cópia Automática da Resposta para a Área de Transferência", "Auto-playback response": "Reprodução automática da resposta", "Autocomplete Generation": "", "Autocomplete Generation Input Max Length": "", "Automatic1111": "", "AUTOMATIC1111 Api Auth String": "", "AUTOMATIC1111 Base URL": "URL Base do AUTOMATIC1111", "AUTOMATIC1111 Base URL is required.": "O URL Base do AUTOMATIC1111 é obrigatório.", "Available list": "", "Available Tools": "", "available!": "disponível!", "Awful": "", "Azure AI Speech": "", "Azure Region": "", "Back": "Voltar", "Bad Response": "Resposta má", "Banners": "Estandartes", "Base Model (From)": "Modelo Base (De)", "Base Model List Cache speeds up access by fetching base models only at startup or on settings save—faster, but may not show recent base model changes.": "", "before": "antes", "Being lazy": "<PERSON>", "Beta": "", "Bing Search V7 Endpoint": "", "Bing Search V7 Subscription Key": "", "Bocha Search API Key": "", "Bold": "", "Boosting or penalizing specific tokens for constrained responses. Bias values will be clamped between -100 and 100 (inclusive). (Default: none)": "", "Both Docling OCR Engine and Language(s) must be provided or both left empty.": "", "Brave Search API Key": "<PERSON><PERSON> da <PERSON> de Pesquisa Brave", "Bullet List": "", "By {{name}}": "", "Bypass Embedding and Retrieval": "", "Bypass Web Loader": "", "Cache Base Model List": "", "Calendar": "", "Call": "<PERSON><PERSON>", "Call feature is not supported when using Web STT engine": "A funcionalide de Chamar não é suportada quando usa um motor Web STT", "Camera": "Camera", "Cancel": "<PERSON><PERSON><PERSON>", "Capabilities": "Capacidades", "Capture": "", "Capture Audio": "", "Certificate Path": "", "Change Password": "<PERSON><PERSON><PERSON>", "Channel Name": "", "Channels": "", "Character": "", "Character limit for autocomplete generation input": "", "Chart new frontiers": "", "Chat": "Conversa", "Chat Background Image": "", "Chat Bubble UI": "Bolha UI da Conversa", "Chat Controls": "", "Chat direction": "Direção da Conversa", "Chat Overview": "", "Chat Permissions": "", "Chat Tags Auto-Generation": "", "Chats": "Conversas", "Check Again": "Verifique novamente", "Check for updates": "Verificar atualizações", "Checking for updates...": "Verificando atualizações...", "Choose a model before saving...": "Escolha um modelo antes de guardar...", "Chunk Overlap": "Sobreposição de Fragmento", "Chunk Size": "Tamanho do Fragmento", "Ciphers": "", "Citation": "Citação", "Citations": "", "Clear memory": "<PERSON><PERSON>", "Clear Memory": "", "click here": "", "Click here for filter guides.": "", "Click here for help.": "Clique aqui para obter ajuda.", "Click here to": "Clique aqui para", "Click here to download user import template file.": "", "Click here to learn more about faster-whisper and see the available models.": "", "Click here to see available models.": "", "Click here to select": "Clique aqui para selecionar", "Click here to select a csv file.": "Clique aqui para selecionar um ficheiro csv.", "Click here to select a py file.": "Clique aqui para selecionar um ficheiro py", "Click here to upload a workflow.json file.": "", "click here.": "clique aqui.", "Click on the user role button to change a user's role.": "Clique no botão de função do utilizador para alterar a função de um utilizador.", "Clipboard write permission denied. Please check your browser settings to grant the necessary access.": "", "Clone": "Clonar", "Clone Chat": "", "Clone of {{TITLE}}": "", "Close": "<PERSON><PERSON><PERSON>", "Close Configure Connection Modal": "", "Close modal": "", "Close settings modal": "", "Code Block": "", "Code execution": "", "Code Execution": "", "Code Execution Engine": "", "Code Execution Timeout": "", "Code formatted successfully": "", "Code Interpreter": "", "Code Interpreter Engine": "", "Code Interpreter Prompt Template": "", "Collapse": "", "Collection": "Coleção", "Color": "", "ComfyUI": "ComfyUI", "ComfyUI API Key": "", "ComfyUI Base URL": "URL Base do ComfyUI", "ComfyUI Base URL is required.": "O URL Base do ComfyUI é obrigatório.", "ComfyUI Workflow": "", "ComfyUI Workflow Nodes": "", "Command": "Comand<PERSON>", "Comment": "", "Completions": "", "Concurrent Requests": "Solicitações simultâneas", "Configure": "", "Confirm": "", "Confirm Password": "Confirmar <PERSON>", "Confirm your action": "", "Confirm your new password": "", "Connect to your own OpenAI compatible API endpoints.": "", "Connect to your own OpenAPI compatible external tool servers.": "", "Connection failed": "", "Connection successful": "", "Connection Type": "", "Connections": "Conexões", "Connections saved successfully": "", "Connections settings updated": "", "Constrains effort on reasoning for reasoning models. Only applicable to reasoning models from specific providers that support reasoning effort.": "", "Contact Admin for WebUI Access": "Contatar Admin para acesso ao WebUI", "Content": "<PERSON><PERSON><PERSON><PERSON>", "Content Extraction Engine": "", "Continue Response": "Contin<PERSON><PERSON> resposta", "Continue with {{provider}}": "", "Continue with Email": "", "Continue with LDAP": "", "Control how message text is split for TTS requests. 'Punctuation' splits into sentences, 'paragraphs' splits into paragraphs, and 'none' keeps the message as a single string.": "", "Control the repetition of token sequences in the generated text. A higher value (e.g., 1.5) will penalize repetitions more strongly, while a lower value (e.g., 1.1) will be more lenient. At 1, it is disabled.": "", "Controls": "", "Controls the balance between coherence and diversity of the output. A lower value will result in more focused and coherent text.": "", "Copied": "", "Copied link to clipboard": "", "Copied shared chat URL to clipboard!": "URL de Conversa partilhado copiada com sucesso!", "Copied to clipboard": "", "Copy": "Copiar", "Copy Formatted Text": "", "Copy last code block": "Copiar último bloco de código", "Copy last response": "Copiar última resposta", "Copy link": "", "Copy Link": "Copiar link", "Copy to clipboard": "", "Copying to clipboard was successful!": "Cópia para a área de transferência bem-sucedida!", "CORS must be properly configured by the provider to allow requests from Open WebUI.": "", "Create": "", "Create a knowledge base": "", "Create a model": "<PERSON><PERSON>r um modelo", "Create Account": "<PERSON><PERSON><PERSON>", "Create Admin Account": "", "Create Channel": "", "Create Group": "", "Create Knowledge": "", "Create new key": "Criar nova chave", "Create new secret key": "Criar nova chave secreta", "Create Note": "", "Create your first note by clicking on the plus button below.": "", "Created at": "C<PERSON><PERSON> em", "Created At": "C<PERSON><PERSON> em", "Created by": "", "CSV Import": "", "Ctrl+Enter to Send": "", "Current Model": "<PERSON><PERSON>", "Current Password": "<PERSON><PERSON>", "Custom": "Personalizado", "Custom description enabled": "", "Custom Parameter Name": "", "Custom Parameter Value": "", "Danger Zone": "", "Dark": "Escuro", "Database": "Base de dados", "Datalab Marker API": "", "Datalab Marker API Key required.": "", "DD/MM/YYYY": "", "December": "Dezembro", "Default": "Padrão", "Default (Open AI)": "", "Default (SentenceTransformers)": "Padrão (SentenceTransformers)", "Default description enabled": "", "Default mode works with a wider range of models by calling tools once before execution. Native mode leverages the model's built-in tool-calling capabilities, but requires the model to inherently support this feature.": "", "Default Model": "<PERSON><PERSON>", "Default model updated": "<PERSON><PERSON> atualizado", "Default Models": "", "Default permissions": "", "Default permissions updated successfully": "", "Default Prompt Suggestions": "Sugestões de Prompt Padrão", "Default to 389 or 636 if TLS is enabled": "", "Default to ALL": "", "Default to segmented retrieval for focused and relevant content extraction, this is recommended for most cases.": "", "Default User Role": "Função de Utilizador Padrão", "Delete": "<PERSON><PERSON><PERSON>", "Delete a model": "<PERSON><PERSON><PERSON> um modelo", "Delete All Chats": "<PERSON><PERSON><PERSON> to<PERSON> as conversas", "Delete All Models": "", "Delete chat": "Apagar conversa", "Delete Chat": "Apagar Conversa", "Delete chat?": "", "Delete folder?": "", "Delete function?": "", "Delete Message": "", "Delete message?": "", "Delete note?": "", "Delete prompt?": "", "delete this link": "apagar este link", "Delete tool?": "", "Delete User": "<PERSON><PERSON><PERSON> Util<PERSON>", "Deleted {{deleteModelTag}}": "{{deleteModelTag}} a<PERSON><PERSON>", "Deleted {{name}}": "<PERSON><PERSON><PERSON> {{name}}", "Deleted User": "", "Deployment names are required for Azure OpenAI": "", "Describe Pictures in Documents": "", "Describe your knowledge base and objectives": "", "Description": "Descrição", "Detect Artifacts Automatically": "", "Dictate": "", "Didn't fully follow instructions": "Não seguiu instruções com precisão", "Direct": "", "Direct Connections": "", "Direct Connections allow users to connect to their own OpenAI compatible API endpoints.": "", "Direct Tool Servers": "", "Disable Code Interpreter": "", "Disable Image Extraction": "", "Disable image extraction from the PDF. If Use LLM is enabled, images will be automatically captioned. Defaults to False.": "", "Disabled": "", "Discover a function": "", "Discover a model": "Descubra um modelo", "Discover a prompt": "Descobrir um prompt", "Discover a tool": "", "Discover how to use Open WebUI and seek support from the community.": "", "Discover wonders": "", "Discover, download, and explore custom functions": "", "Discover, download, and explore custom prompts": "<PERSON><PERSON><PERSON>, des<PERSON><PERSON><PERSON> e explore prompts personalizados", "Discover, download, and explore custom tools": "", "Discover, download, and explore model presets": "Des<PERSON><PERSON>, descarregue e explore predefinições de modelo", "Display": "", "Display Emoji in Call": "", "Display the username instead of You in the Chat": "Exibir o nome de utilizador em vez de Você na Conversa", "Displays citations in the response": "", "Dive into knowledge": "", "Do not install functions from sources you do not fully trust.": "", "Do not install tools from sources you do not fully trust.": "", "Docling": "", "Docling Server URL required.": "", "Document": "Documento", "Document Intelligence": "", "Document Intelligence endpoint and key required.": "", "Documentation": "Documentação", "Documents": "Documentos", "does not make any external connections, and your data stays securely on your locally hosted server.": "não faz conexões externas e os seus dados permanecem seguros no seu servidor alojado localmente.", "Domain Filter List": "", "Don't have an account?": "Não tem uma conta?", "don't install random functions from sources you don't trust.": "", "don't install random tools from sources you don't trust.": "", "Don't like the style": "Não gosta do estilo", "Done": "", "Download": "<PERSON><PERSON><PERSON><PERSON>", "Download as SVG": "", "Download canceled": "Download cancelado", "Download Database": "Descarregar Base de Dados", "Drag and drop a file to upload or select a file to view": "", "Draw": "", "Drop any files here to upload": "", "e.g. '30s','10m'. Valid time units are 's', 'm', 'h'.": "por exemplo, '30s', '10m'. Unidades de tempo válidas são 's', 'm', 'h'.", "e.g. \"json\" or a JSON schema": "", "e.g. 60": "", "e.g. A filter to remove profanity from text": "", "e.g. en": "", "e.g. My Filter": "", "e.g. My Tools": "", "e.g. my_filter": "", "e.g. my_tools": "", "e.g. pdf, docx, txt": "", "e.g. Tools for performing various operations": "", "e.g., 3, 4, 5 (leave blank for default)": "", "e.g., audio/wav,audio/mpeg,video/* (leave blank for defaults, * for all)": "", "e.g., en-US,ja-JP (leave blank for auto-detect)": "", "e.g., westus (leave blank for eastus)": "", "e.g.) en,fr,de": "", "Edit": "<PERSON><PERSON>", "Edit Arena Model": "", "Edit Channel": "", "Edit Connection": "", "Edit Default Permissions": "", "Edit Folder": "", "Edit Memory": "", "Edit User": "<PERSON><PERSON>", "Edit User Group": "", "Edited": "", "Editing": "", "Eject": "", "ElevenLabs": "", "Email": "E-mail", "Embark on adventures": "", "Embedding": "", "Embedding Batch Size": "Tamanho do Lote do Embedding", "Embedding Model": "Modelo de Embedding", "Embedding Model Engine": "Motor de Modelo de Embedding", "Embedding model set to \"{{embedding_model}}\"": "Modelo de Embedding definido como \"{{embedding_model}}\"", "Enable API Key": "", "Enable autocomplete generation for chat messages": "", "Enable Code Execution": "", "Enable Code Interpreter": "", "Enable Community Sharing": "Active a Partilha da Comunidade", "Enable Memory Locking (mlock) to prevent model data from being swapped out of RAM. This option locks the model's working set of pages into RAM, ensuring that they will not be swapped out to disk. This can help maintain performance by avoiding page faults and ensuring fast data access.": "", "Enable Memory Mapping (mmap) to load model data. This option allows the system to use disk storage as an extension of RAM by treating disk files as if they were in RAM. This can improve model performance by allowing for faster data access. However, it may not work correctly with all systems and can consume a significant amount of disk space.": "", "Enable Message Rating": "", "Enable Mirostat sampling for controlling perplexity.": "", "Enable New Sign Ups": "Ativar Novas Inscrições", "Enabled": "", "Endpoint URL": "", "Enforce Temporary Chat": "", "Enhance": "", "Ensure your CSV file includes 4 columns in this order: Name, Email, Password, Role.": "Confirme que o seu ficheiro CSV inclui 4 colunas nesta ordem: Nome, E-mail, Senha, Função.", "Enter {{role}} message here": "Escreva a mensagem de {{role}} aqui", "Enter a detail about yourself for your LLMs to recall": "Escreva um detalhe sobre você para que os seus LLMs possam lembrar-se", "Enter a title for the pending user info overlay. Leave empty for default.": "", "Enter a watermark for the response. Leave empty for none.": "", "Enter api auth string (e.g. username:password)": "", "Enter Application DN": "", "Enter Application DN Password": "", "Enter Bing Search V7 Endpoint": "", "Enter Bing Search V7 Subscription Key": "", "Enter BM25 Weight": "", "Enter Bocha Search API Key": "", "Enter Brave Search API Key": "Escreva a chave da API do Brave Search", "Enter certificate path": "", "Enter CFG Scale (e.g. 7.0)": "", "Enter Chunk Overlap": "Escreva a Sobreposição de Fragmento", "Enter Chunk Size": "Escreva o Tamanho do Fragmento", "Enter comma-separated \"token:bias_value\" pairs (example: 5432:100, 413:-100)": "", "Enter Config in JSON format": "", "Enter content for the pending user info overlay. Leave empty for default.": "", "Enter Datalab Marker API Key": "", "Enter description": "", "Enter Docling OCR Engine": "", "Enter Docling OCR Language(s)": "", "Enter Docling Server URL": "", "Enter Document Intelligence Endpoint": "", "Enter Document Intelligence Key": "", "Enter domains separated by commas (e.g., example.com,site.org)": "", "Enter Exa API Key": "", "Enter External Document Loader API Key": "", "Enter External Document Loader URL": "", "Enter External Web Loader API Key": "", "Enter External Web Loader URL": "", "Enter External Web Search API Key": "", "Enter External Web Search URL": "", "Enter Firecrawl API Base URL": "", "Enter Firecrawl API Key": "", "Enter folder name": "", "Enter Github Raw URL": "Escreva o URL cru do Github", "Enter Google PSE API Key": "Escreva a chave da API PSE do Google", "Enter Google PSE Engine Id": "Escreva o ID do mecanismo PSE do Google", "Enter Image Size (e.g. 512x512)": "Escreva o Tamanho da Imagem (por exemplo, 512x512)", "Enter Jina API Key": "", "Enter Jupyter Password": "", "Enter Jupyter Token": "", "Enter Jupyter URL": "", "Enter Kagi Search API Key": "", "Enter Key Behavior": "", "Enter language codes": "Escreva os códigos de idioma", "Enter Mistral API Key": "", "Enter Model ID": "", "Enter model tag (e.g. {{modelTag}})": "Escreva a tag do modelo (por exemplo, {{modelTag}})", "Enter Mojeek Search API Key": "", "Enter name": "", "Enter New Password": "", "Enter Number of Steps (e.g. 50)": "Escreva o Número de Etapas (por exemplo, 50)", "Enter Perplexity API Key": "", "Enter Playwright Timeout": "", "Enter Playwright WebSocket URL": "", "Enter proxy URL (e.g. **************************:port)": "", "Enter reasoning effort": "", "Enter Sampler (e.g. Euler a)": "", "Enter Scheduler (e.g. Karras)": "", "Enter Score": "Escreva a Pontuação", "Enter SearchApi API Key": "", "Enter SearchApi Engine": "", "Enter Searxng Query URL": "Escreva o URL da Pesquisa Searxng", "Enter Seed": "", "Enter SerpApi API Key": "", "Enter SerpApi Engine": "", "Enter Serper API Key": "Escreva a chave da API Serper", "Enter Serply API Key": "", "Enter Serpstack API Key": "Escreva a chave da <PERSON>ps<PERSON>ck", "Enter server host": "", "Enter server label": "", "Enter server port": "", "Enter Sougou Search API sID": "", "Enter Sougou Search API SK": "", "Enter stop sequence": "Escreva a sequência de paragem", "Enter system prompt": "", "Enter system prompt here": "", "Enter Tavily API Key": "", "Enter Tavily Extract Depth": "", "Enter the public URL of your WebUI. This URL will be used to generate links in the notifications.": "", "Enter the URL of the function to import": "", "Enter the URL to import": "", "Enter Tika Server URL": "", "Enter timeout in seconds": "", "Enter to Send": "", "Enter Top K": "Escreva o Top K", "Enter Top K Reranker": "", "Enter URL (e.g. http://127.0.0.1:7860/)": "Escreva o URL (por exemplo, http://127.0.0.1:7860/)", "Enter URL (e.g. http://localhost:11434)": "Escreva o URL (por exemplo, http://localhost:11434)", "Enter Yacy Password": "", "Enter Yacy URL (e.g. http://yacy.example.com:8090)": "", "Enter Yacy Username": "", "Enter your current password": "", "Enter Your Email": "Escreva o seu E-mail", "Enter Your Full Name": "Escreva o seu Nome Completo", "Enter your message": "", "Enter your name": "", "Enter Your Name": "", "Enter your new password": "", "Enter Your Password": "Escreva a sua Senha", "Enter Your Role": "Escreva a sua Função", "Enter Your Username": "", "Enter your webhook URL": "", "Error": "Erro", "ERROR": "", "Error accessing Google Drive: {{error}}": "", "Error accessing media devices.": "", "Error starting recording.": "", "Error unloading model: {{error}}": "", "Error uploading file: {{error}}": "", "Evaluations": "", "Everyone": "", "Exa API Key": "", "Example: (&(objectClass=inetOrgPerson)(uid=%s))": "", "Example: ALL": "", "Example: mail": "", "Example: ou=users,dc=foo,dc=example": "", "Example: sAMAccountName or uid or userPrincipalName": "", "Exceeded the number of seats in your license. Please contact support to increase the number of seats.": "", "Exclude": "", "Execute code for analysis": "", "Executing **{{NAME}}**...": "", "Expand": "", "Experimental": "Experimental", "Explain": "", "Explore the cosmos": "", "Export": "Exportar", "Export All Archived Chats": "", "Export All Chats (All Users)": "Exporta<PERSON> as <PERSON><PERSON><PERSON> (Todos os Utilizadores)", "Export chat (.json)": "Exportar Conversa (.json)", "Export Chats": "Exportar Conversas", "Export Config to JSON File": "", "Export Functions": "", "Export Models": "Modelos de Exportação", "Export Presets": "", "Export Prompt Suggestions": "", "Export Prompts": "Exportar Prompts", "Export to CSV": "", "Export Tools": "", "External": "", "External Document Loader URL required.": "", "External Task Model": "", "External Web Loader API Key": "", "External Web Loader URL": "", "External Web Search API Key": "", "External Web Search URL": "", "Failed to add file.": "", "Failed to connect to {{URL}} OpenAPI tool server": "", "Failed to copy link": "", "Failed to create API Key.": "Falha ao criar a Chave da <PERSON>.", "Failed to delete note": "", "Failed to extract content from the file: {{error}}": "", "Failed to extract content from the file.": "", "Failed to fetch models": "", "Failed to generate title": "", "Failed to load file content.": "", "Failed to read clipboard contents": "Falha ao ler o conteúdo da área de transferência", "Failed to save connections": "", "Failed to save models configuration": "", "Failed to update settings": "Falha ao atualizar as definições", "Failed to upload file.": "", "Features": "", "Features Permissions": "", "February": "<PERSON><PERSON>", "Feedback Details": "", "Feedback History": "", "Feedbacks": "", "Feel free to add specific details": "Sinta-se <PERSON> vontade para adicionar detalhes específicos", "File": "", "File added successfully.": "", "File content updated successfully.": "", "File Mode": "<PERSON><PERSON>", "File not found.": "Ficheiro não encontrado.", "File removed successfully.": "", "File size should not exceed {{maxSize}} MB.": "", "File Upload": "", "File uploaded successfully": "", "Files": "", "Filter": "", "Filter is now globally disabled": "", "Filter is now globally enabled": "", "Filters": "", "Fingerprint spoofing detected: Unable to use initials as avatar. Defaulting to default profile image.": "Detectada falsificação da impressão digital: Não é possível usar iniciais como avatar. A usar a imagem de perfil padrão.", "Firecrawl API Base URL": "", "Firecrawl API Key": "", "Fluidly stream large external response chunks": "Transmita com fluidez grandes blocos de resposta externa", "Focus chat input": "Focar na conversa", "Folder deleted successfully": "", "Folder Name": "", "Folder name cannot be empty.": "", "Folder name updated successfully": "", "Folder updated successfully": "", "Follow up": "", "Follow Up Generation": "", "Follow Up Generation Prompt": "", "Follow-Up Auto-Generation": "", "Followed instructions perfectly": "Seguiu instruções perfeitamente", "Force OCR": "", "Force OCR on all pages of the PDF. This can lead to worse results if you have good text in your PDFs. Defaults to False.": "", "Forge new paths": "", "Form": "", "Format your variables using brackets like this:": "", "Forwards system user session credentials to authenticate": "", "Full Context Mode": "", "Function": "", "Function Calling": "", "Function created successfully": "", "Function deleted successfully": "", "Function Description": "", "Function ID": "", "Function imported successfully": "", "Function is now globally disabled": "", "Function is now globally enabled": "", "Function Name": "", "Function updated successfully": "", "Functions": "", "Functions allow arbitrary code execution.": "", "Functions imported successfully": "", "Gemini": "", "Gemini API Config": "", "Gemini API Key is required.": "", "General": "G<PERSON>", "Generate": "", "Generate an image": "", "Generate Image": "<PERSON><PERSON><PERSON> imagem", "Generate prompt pair": "", "Generating search query": "A gerar a consulta da pesquisa", "Generating...": "", "Get information on {{name}} in the UI": "", "Get started": "", "Get started with {{WEBUI_NAME}}": "", "Global": "", "Good Response": "Boa Resposta", "Google Drive": "", "Google PSE API Key": "Chave da API PSE do Google", "Google PSE Engine Id": "ID do mecanismo PSE do Google", "Group created successfully": "", "Group deleted successfully": "", "Group Description": "", "Group Name": "", "Group updated successfully": "", "Groups": "", "H1": "", "H2": "", "H3": "", "Haptic Feedback": "", "Hello, {{name}}": "<PERSON><PERSON><PERSON>, {{name}}", "Help": "<PERSON><PERSON><PERSON>", "Help us create the best community leaderboard by sharing your feedback history!": "", "Hex Color": "", "Hex Color - Leave empty for default color": "", "Hide": "Ocultar", "Hide from Sidebar": "", "Hide Model": "", "High Contrast Mode": "", "Home": "", "Host": "", "How can I help you today?": "Como posso ajudá-lo hoje?", "How would you rate this response?": "", "HTML": "", "Hybrid Search": "Pesquisa Híbrida", "I acknowledge that I have read and I understand the implications of my action. I am aware of the risks associated with executing arbitrary code and I have verified the trustworthiness of the source.": "", "ID": "", "iframe Sandbox Allow Forms": "", "iframe Sandbox Allow Same Origin": "", "Ignite curiosity": "", "Image": "", "Image Compression": "", "Image Compression Height": "", "Image Compression Width": "", "Image Generation": "", "Image Generation (Experimental)": "Geração de Imagens (Experimental)", "Image Generation Engine": "Mecanismo de Geração de Imagens", "Image Max Compression Size": "", "Image Max Compression Size height": "", "Image Max Compression Size width": "", "Image Prompt Generation": "", "Image Prompt Generation Prompt": "", "Image Settings": "Configuraç<PERSON><PERSON> da Imagem", "Images": "Imagens", "Import": "", "Import Chats": "Importar Conversas", "Import Config from JSON File": "", "Import From Link": "", "Import Functions": "", "Import Models": "Importar Modelos", "Import Notes": "", "Import Presets": "", "Import Prompt Suggestions": "", "Import Prompts": "Importar Prompts", "Import Tools": "", "Include": "", "Include `--api-auth` flag when running stable-diffusion-webui": "", "Include `--api` flag when running stable-diffusion-webui": "Inclua a flag `--api` ao executar stable-diffusion-webui", "Influences how quickly the algorithm responds to feedback from the generated text. A lower learning rate will result in slower adjustments, while a higher learning rate will make the algorithm more responsive.": "", "Info": "Informação", "Inject the entire content as context for comprehensive processing, this is recommended for complex queries.": "", "Input commands": "Comandos de entrada", "Input Variables": "", "Insert": "", "Insert Prompt as Rich Text": "", "Install from Github URL": "Instalar a partir do URL do Github", "Instant Auto-Send After Voice Transcription": "Enviar automaticamente depois da transcrição da voz", "Integration": "", "Interface": "Interface", "Invalid file content": "", "Invalid file format.": "", "Invalid JSON file": "", "Invalid Tag": "Etiqueta <PERSON>álid<PERSON>", "is typing...": "", "Italic": "", "January": "Janeiro", "Jina API Key": "", "join our Discord for help.": "junte-se ao nosso Discord para obter ajuda.", "JSON": "JSON", "JSON Preview": "Pré-visualização JSON", "July": "<PERSON><PERSON>", "June": "<PERSON><PERSON>", "Jupyter Auth": "", "Jupyter URL": "", "JWT Expiration": "Expiração JWT", "JWT Token": "Token JWT", "Kagi Search API Key": "", "Keep in Sidebar": "", "Key": "", "Keyboard shortcuts": "Atalhos de teclado", "Knowledge": "Conhecimento", "Knowledge Access": "", "Knowledge Base": "", "Knowledge created successfully.": "", "Knowledge deleted successfully.": "", "Knowledge Public Sharing": "", "Knowledge reset successfully.": "", "Knowledge updated successfully": "", "Kokoro.js (Browser)": "", "Kokoro.js Dtype": "", "Label": "", "Landing Page Mode": "", "Language": "Idioma", "Language Locales": "", "Languages": "", "Last Active": "Último Ativo", "Last Modified": "", "Last reply": "", "LDAP": "", "LDAP server updated": "", "Leaderboard": "", "Learn more about OpenAPI tool servers.": "", "Leave empty for no compression": "", "Leave empty for unlimited": "", "Leave empty to include all models from \"{{url}}\" endpoint": "", "Leave empty to include all models from \"{{url}}/api/tags\" endpoint": "", "Leave empty to include all models from \"{{url}}/models\" endpoint": "", "Leave empty to include all models or select specific models": "", "Leave empty to use the default prompt, or enter a custom prompt": "", "Leave model field empty to use the default model.": "", "License": "", "Light": "<PERSON><PERSON><PERSON>", "Listening...": "A escutar...", "Llama.cpp": "", "LLMs can make mistakes. Verify important information.": "LLMs podem cometer erros. Verifique informações importantes.", "Loader": "", "Loading Kokoro.js...": "", "Local": "", "Local Task Model": "", "Location access not allowed": "", "Lost": "", "LTR": "LTR", "Made by Open WebUI Community": "<PERSON>ito pela Comunidade OpenWebUI", "Make password visible in the user interface": "", "Make sure to enclose them with": "Certifique-se de colocá-los entre", "Make sure to export a workflow.json file as API format from ComfyUI.": "", "Manage": "<PERSON><PERSON><PERSON>", "Manage Direct Connections": "", "Manage Models": "", "Manage Ollama": "", "Manage Ollama API Connections": "", "Manage OpenAI API Connections": "", "Manage Pipelines": "Gerir pipelines", "Manage Tool Servers": "", "March": "Março", "Markdown": "", "Markdown (Header)": "", "Max Speakers": "", "Max Upload Count": "", "Max Upload Size": "", "Maximum of 3 models can be downloaded simultaneously. Please try again later.": "O máximo de 3 modelos podem ser descarregados simultaneamente. Tente novamente mais tarde.", "May": "<PERSON><PERSON>", "Memories accessible by LLMs will be shown here.": "Memórias acessíveis por LLMs serão mostradas aqui.", "Memory": "Memória", "Memory added successfully": "", "Memory cleared successfully": "", "Memory deleted successfully": "", "Memory updated successfully": "", "Merge Responses": "", "Merged Response": "Resposta Fundida", "Message rating should be enabled to use this feature": "", "Messages you send after creating your link won't be shared. Users with the URL will be able to view the shared chat.": "Mensagens que você enviar após criar o seu link não serão partilhadas. Os utilizadores com o URL poderão visualizar a conversa partilhada.", "Microsoft OneDrive": "", "Microsoft OneDrive (personal)": "", "Microsoft OneDrive (work/school)": "", "Mistral OCR": "", "Mistral OCR API Key required.": "", "Model": "", "Model '{{modelName}}' has been successfully downloaded.": "O modelo '{{modelName}}' foi descarregado com sucesso.", "Model '{{modelTag}}' is already in queue for downloading.": "O modelo '{{modelTag}}' já está na fila para descarregar.", "Model {{modelId}} not found": "Modelo {{modelId}} não foi encontrado", "Model {{modelName}} is not vision capable": "O modelo {{modelName}} não é capaz de visão", "Model {{name}} is now {{status}}": "Modelo {{name}} agora é {{status}}", "Model {{name}} is now hidden": "", "Model {{name}} is now visible": "", "Model accepts file inputs": "", "Model accepts image inputs": "", "Model can execute code and perform calculations": "", "Model can generate images based on text prompts": "", "Model can search the web for information": "", "Model created successfully!": "", "Model filesystem path detected. Model shortname is required for update, cannot continue.": "Dtectado caminho do sistema de ficheiros do modelo. É necessário o nome curto do modelo para atualização, não é possível continuar.", "Model Filtering": "", "Model ID": "ID do modelo", "Model IDs": "", "Model Name": "", "Model not selected": "Modelo não selecionado", "Model Params": "Params Modelo", "Model Permissions": "", "Model unloaded successfully": "", "Model updated successfully": "", "Model(s) do not support file upload": "", "Modelfile Content": "Conteúdo do Ficheiro do Modelo", "Models": "Modelos", "Models Access": "", "Models configuration saved successfully": "", "Models Public Sharing": "", "Mojeek Search API Key": "", "more": "", "More": "<PERSON><PERSON>", "Name": "Nome", "Name your knowledge base": "", "Native": "", "New Chat": "Nova Conversa", "New Folder": "", "New Function": "", "New Note": "", "New Password": "Nova Senha", "New Tool": "", "new-channel": "", "Next message": "", "No chats found for this user.": "", "No chats found.": "", "No content": "", "No content found": "", "No content found in file.": "", "No content to speak": "", "No distance available": "", "No feedbacks found": "", "No file selected": "", "No groups with access, add a group to grant access": "", "No HTML, CSS, or JavaScript content found.": "", "No inference engine with management support found": "", "No knowledge found": "", "No memories to clear": "", "No model IDs": "", "No models found": "", "No models selected": "", "No Notes": "", "No results found": "Não foram encontrados resultados", "No search query generated": "Não foi gerada nenhuma consulta de pesquisa", "No source available": "Nenhuma fonte disponível", "No users were found.": "", "No valves to update": "", "None": "<PERSON><PERSON><PERSON>", "Not factually correct": "Não é correto em termos factuais", "Not helpful": "", "Note deleted successfully": "", "Note: If you set a minimum score, the search will only return documents with a score greater than or equal to the minimum score.": "Nota: Se você definir uma pontuação mínima, a pesquisa só retornará documentos com uma pontuação maior ou igual à pontuação mínima.", "Notes": "", "Notification Sound": "", "Notification Webhook": "", "Notifications": "Notificações da Área de Trabalho", "November": "Novembro", "OAuth ID": "", "October": "Out<PERSON>ro", "Off": "Des<PERSON><PERSON>", "Okay, Let's Go!": "Ok, Vamos Lá!", "OLED Dark": "OLED Escuro", "Ollama": "Ollama", "Ollama API": "Ollama API", "Ollama API settings updated": "", "Ollama Version": "Versão do Ollama", "On": "Ligado", "OneDrive": "", "Only active when \"Paste Large Text as File\" setting is toggled on.": "", "Only active when the chat input is in focus and an LLM is generating a response.": "", "Only alphanumeric characters and hyphens are allowed": "", "Only alphanumeric characters and hyphens are allowed in the command string.": "Apenas caracteres alfanuméricos e hífens são permitidos na string de comando.", "Only collections can be edited, create a new knowledge base to edit/add documents.": "", "Only markdown files are allowed": "", "Only select users and groups with permission can access": "", "Oops! Looks like the URL is invalid. Please double-check and try again.": "Epá! Parece que o URL é inválido. Verifique novamente e tente outra vez.", "Oops! There are files still uploading. Please wait for the upload to complete.": "", "Oops! There was an error in the previous response.": "", "Oops! You're using an unsupported method (frontend only). Please serve the WebUI from the backend.": "Epá! Você está a usar um método não suportado (somente frontend). Por favor, sirva o WebUI a partir do backend.", "Open file": "", "Open in full screen": "", "Open modal to configure connection": "", "Open new chat": "Abrir nova conversa", "Open WebUI can use tools provided by any OpenAPI server.": "", "Open WebUI uses faster-whisper internally.": "", "Open WebUI uses SpeechT5 and CMU Arctic speaker embeddings.": "", "Open WebUI version (v{{OPEN_WEBUI_VERSION}}) is lower than required version (v{{REQUIRED_VERSION}})": "", "OpenAI": "OpenAI", "OpenAI API": "API OpenAI", "OpenAI API Config": "Configuração da API OpenAI", "OpenAI API Key is required.": "A Chave da API OpenAI é obrigatória.", "OpenAI API settings updated": "", "OpenAI URL/Key required.": "URL/Chave da API OpenAI é necessária.", "openapi.json URL or Path": "", "Options for running a local vision-language model in the picture description. The parameters refer to a model hosted on Hugging Face. This parameter is mutually exclusive with picture_description_api.": "", "or": "ou", "Ordered List": "", "Organize your users": "", "Other": "Outro", "OUTPUT": "", "Output format": "", "Output Format": "", "Overview": "", "page": "", "Paginate": "", "Parameters": "", "Password": "<PERSON><PERSON>", "Paste Large Text as File": "", "PDF document (.pdf)": "Documento PDF (.pdf)", "PDF Extract Images (OCR)": "Extrair Imagens de PDF (OCR)", "pending": "pendente", "Pending": "", "Pending User Overlay Content": "", "Pending User Overlay Title": "", "Permission denied when accessing media devices": "A permissão foi negada ao aceder aos dispositivos de media", "Permission denied when accessing microphone": "A permissão foi negada ao aceder ao microfone", "Permission denied when accessing microphone: {{error}}": "A permissão foi negada ao aceder o microfone: {{error}}", "Permissions": "", "Perplexity API Key": "", "Perplexity Model": "", "Perplexity Search Context Usage": "", "Personalization": "Personalização", "Picture Description API Config": "", "Picture Description Local Config": "", "Picture Description Mode": "", "Pin": "", "Pinned": "", "Pioneer insights": "", "Pipe": "", "Pipeline deleted successfully": "", "Pipeline downloaded successfully": "", "Pipelines": "<PERSON><PERSON><PERSON>", "Pipelines Not Detected": "", "Pipelines Valves": "Válvulas de Condutas", "Plain text (.md)": "", "Plain text (.txt)": "Texto sem formatação (.txt)", "Playground": "<PERSON><PERSON><PERSON>", "Playwright Timeout (ms)": "", "Playwright WebSocket URL": "", "Please carefully review the following warnings:": "", "Please do not close the settings page while loading the model.": "", "Please enter a prompt": "", "Please enter a valid path": "", "Please enter a valid URL": "", "Please fill in all fields.": "", "Please select a model first.": "", "Please select a model.": "", "Please select a reason": "", "Port": "", "Positive attitude": "Atitude Positiva", "Prefix ID": "", "Prefix ID is used to avoid conflicts with other connections by adding a prefix to the model IDs - leave empty to disable": "", "Prevent file creation": "", "Preview": "", "Previous 30 days": "Últimos 30 dias", "Previous 7 days": "Últimos 7 dias", "Previous message": "", "Private": "", "Profile Image": "<PERSON><PERSON>", "Prompt": "", "Prompt (e.g. Tell me a fun fact about the Roman Empire)": "Prompt (ex.: Dê-me um facto divertido sobre o Império <PERSON>)", "Prompt Autocompletion": "", "Prompt Content": "Conteúdo do Prompt", "Prompt created successfully": "", "Prompt suggestions": "Sugestões de Prompt", "Prompt updated successfully": "", "Prompts": "Prompts", "Prompts Access": "", "Prompts Public Sharing": "", "Public": "", "Pull \"{{searchValue}}\" from Ollama.com": "Puxar \"{{searchValue}}\" do Ollama.com", "Pull a model from Ollama.com": "Puxar um modelo do Ollama.com", "Query Generation Prompt": "", "RAG Template": "Modelo RAG", "Rating": "", "Re-rank models by topic similarity": "", "Read": "", "Read Aloud": "Ler <PERSON> Voz Alta", "Reason": "", "Reasoning Effort": "", "Record": "", "Record voice": "<PERSON><PERSON><PERSON> voz", "Redirecting you to Open WebUI Community": "Redirecionando-o para a Comunidade OpenWebUI", "Reduces the probability of generating nonsense. A higher value (e.g. 100) will give more diverse answers, while a lower value (e.g. 10) will be more conservative.": "", "Refer to yourself as \"User\" (e.g., \"User is learning Spanish\")": "Refera-se a si próprio como \"User\" (por exemplo, \"User está a aprender Espanhol\")", "References from": "", "Refused when it shouldn't have": "Recusado quando não deveria", "Regenerate": "<PERSON><PERSON><PERSON>", "Reindex": "", "Reindex Knowledge Base Vectors": "", "Release Notes": "Notas de Lançamento", "Releases": "", "Relevance": "", "Relevance Threshold": "", "Remember Dismissal": "", "Remove": "Remover", "Remove {{MODELID}} from list.": "", "Remove file": "", "Remove File": "", "Remove image": "", "Remove Model": "Remover Modelo", "Remove this tag from list": "", "Rename": "Renomear", "Reorder Models": "", "Reply in Thread": "", "Reranking Engine": "", "Reranking Model": "<PERSON><PERSON>", "Reset": "", "Reset All Models": "", "Reset Upload Directory": "Limpar Pasta de Carregamento", "Reset Vector Storage/Knowledge": "", "Reset view": "", "Response": "", "Response notifications cannot be activated as the website permissions have been denied. Please visit your browser settings to grant the necessary access.": "", "Response splitting": "", "Response Watermark": "", "Result": "", "Retrieval": "", "Retrieval Query Generation": "", "Rich Text Input for Chat": "", "RK": "", "Role": "Função", "Rosé Pine": "<PERSON><PERSON><PERSON>", "Rosé Pine Dawn": "<PERSON><PERSON><PERSON>", "RTL": "RTL", "Run": "", "Running": "A correr", "Save": "Guardar", "Save & Create": "Guardar e Criar", "Save & Update": "Guardar e Atualizar", "Save As Copy": "", "Save Tag": "", "Saved": "", "Saving chat logs directly to your browser's storage is no longer supported. Please take a moment to download and delete your chat logs by clicking the button below. Don't worry, you can easily re-import your chat logs to the backend through": "Guardar o registo das conversas diretamente no armazenamento do seu navegador já não é suportado. Reserve um momento para descarregar e eliminar os seus registos de conversas clicando no botão abaixo. Não se preocupe, você pode facilmente reimportar os seus registos de conversas para o backend através de", "Scroll On Branch Change": "", "Search": "<PERSON><PERSON><PERSON><PERSON>", "Search a model": "Pesquisar um modelo", "Search Base": "", "Search Chats": "<PERSON><PERSON><PERSON><PERSON>", "Search Collection": "", "Search Filters": "", "search for tags": "", "Search Functions": "", "Search Knowledge": "", "Search Models": "Modelos de pesquisa", "Search Notes": "", "Search options": "", "Search Prompts": "Pesquisar Prompts", "Search Result Count": "Contagem de resultados da pesquisa", "Search the internet": "", "Search Tools": "", "SearchApi API Key": "", "SearchApi Engine": "", "Searched {{count}} sites": "", "Searching \"{{searchQuery}}\"": "", "Searching Knowledge for \"{{searchQuery}}\"": "", "Searching the web...": "", "Searxng Query URL": "URL de consulta Searxng", "See readme.md for instructions": "Consulte readme.md para obter instruções", "See what's new": "Veja o que há de novo", "Seed": "Semente", "Select a base model": "Selecione um modelo base", "Select a engine": "Selecione um motor", "Select a function": "", "Select a group": "", "Select a model": "Selecione um modelo", "Select a pipeline": "Selecione um pipeline", "Select a pipeline url": "Selecione um URL de pipeline", "Select a tool": "", "Select an auth method": "", "Select an Ollama instance": "", "Select Engine": "", "Select Knowledge": "", "Select only one model to call": "Selecione apenas um modelo para a chamada", "Selected model(s) do not support image inputs": "O(s) modelo(s) selecionado(s) não suporta(m) entradas de imagem", "Semantic distance to query": "", "Send": "Enviar", "Send a Message": "Enviar uma Mensagem", "Send message": "Enviar mensagem", "Sends `stream_options: { include_usage: true }` in the request.\nSupported providers will return token usage information in the response when set.": "", "September": "Setembro", "SerpApi API Key": "", "SerpApi Engine": "", "Serper API Key": "Chave API Serper", "Serply API Key": "", "Serpstack API Key": "<PERSON><PERSON> da <PERSON>", "Server connection verified": "Conexão com o servidor verificada", "Set as default": "Definir como padrão", "Set CFG Scale": "", "Set Default Model": "Definir Modelo <PERSON>", "Set embedding model": "", "Set embedding model (e.g. {{model}})": "Definir modelo de vetorização (ex.: {{model}})", "Set Image Size": "<PERSON><PERSON><PERSON>", "Set reranking model (e.g. {{model}})": "Definir modelo de reranking (ex.: {{model}})", "Set Sampler": "", "Set Scheduler": "", "Set Steps": "Definir Etapas", "Set the number of layers, which will be off-loaded to GPU. Increasing this value can significantly improve performance for models that are optimized for GPU acceleration but may also consume more power and GPU resources.": "", "Set the number of worker threads used for computation. This option controls how many threads are used to process incoming requests concurrently. Increasing this value can improve performance under high concurrency workloads but may also consume more CPU resources.": "", "Set Voice": "<PERSON><PERSON><PERSON>", "Set whisper model": "", "Sets a flat bias against tokens that have appeared at least once. A higher value (e.g., 1.5) will penalize repetitions more strongly, while a lower value (e.g., 0.9) will be more lenient. At 0, it is disabled.": "", "Sets a scaling bias against tokens to penalize repetitions, based on how many times they have appeared. A higher value (e.g., 1.5) will penalize repetitions more strongly, while a lower value (e.g., 0.9) will be more lenient. At 0, it is disabled.": "", "Sets how far back for the model to look back to prevent repetition.": "", "Sets the random number seed to use for generation. Setting this to a specific number will make the model generate the same text for the same prompt.": "", "Sets the size of the context window used to generate the next token.": "", "Sets the stop sequences to use. When this pattern is encountered, the LLM will stop generating text and return. Multiple stop patterns may be set by specifying multiple separate stop parameters in a modelfile.": "", "Settings": "Configurações", "Settings saved successfully!": "Configurações guardadas com sucesso!", "Share": "Partilhar", "Share Chat": "<PERSON><PERSON><PERSON> Con<PERSON>", "Share to Open WebUI Community": "Partilhar com a Comunidade OpenWebUI", "Sharing Permissions": "", "Shortcuts with an asterisk (*) are situational and only active under specific conditions.": "", "Show": "Mostrar", "Show \"What's New\" modal on login": "", "Show Admin Details in Account Pending Overlay": "Mostrar Detalhes do Administrador na sobreposição de Conta Pendente", "Show All": "", "Show image preview": "", "Show Less": "", "Show Model": "", "Show shortcuts": "<PERSON><PERSON> atalhos", "Show your support!": "", "Showcased creativity": "Criatividade Exibida", "Sign in": "Entrar", "Sign in to {{WEBUI_NAME}}": "", "Sign in to {{WEBUI_NAME}} with LDAP": "", "Sign Out": "<PERSON><PERSON>", "Sign up": "Inscrever-se", "Sign up to {{WEBUI_NAME}}": "", "Significantly improves accuracy by using an LLM to enhance tables, forms, inline math, and layout detection. Will increase latency. Defaults to True.": "", "Signing in to {{WEBUI_NAME}}": "", "sk-1234": "", "Skip Cache": "", "Skip the cache and re-run the inference. Defaults to False.": "", "Sougou Search API sID": "", "Sougou Search API SK": "", "Source": "Fonte", "Speech Playback Speed": "", "Speech recognition error: {{error}}": "Erro de reconhecimento de fala: {{error}}", "Speech-to-Text": "", "Speech-to-Text Engine": "Motor de Fala para Texto", "Stop": "", "Stop Generating": "", "Stop Sequence": "Sequência de Paragem", "Stream Chat Response": "", "Strikethrough": "", "Strip Existing OCR": "", "Strip existing OCR text from the PDF and re-run OCR. Ignored if Force OCR is enabled. Defaults to False.": "", "STT Model": "Modelo STT", "STT Settings": "Configurações STT", "Stylized PDF Export": "", "Subtitle (e.g. about the Roman Empire)": "<PERSON><PERSON><PERSON><PERSON><PERSON> (ex.: sobre o Império <PERSON>)", "Success": "Sucesso", "Successfully updated.": "Atualizado com sucesso.", "Suggested": "Sugerido", "Support": "", "Support this plugin:": "", "Supported MIME Types": "", "Sync directory": "", "System": "Sistema", "System Instructions": "", "System Prompt": "Prompt do Sistema", "Tags": "", "Tags Generation": "", "Tags Generation Prompt": "", "Tail free sampling is used to reduce the impact of less probable tokens from the output. A higher value (e.g., 2.0) will reduce the impact more, while a value of 1.0 disables this setting.": "", "Talk to model": "", "Tap to interrupt": "", "Task List": "", "Task Model": "", "Tasks": "", "Tavily API Key": "", "Tavily Extract Depth": "", "Tell us more:": "Diga-nos mais:", "Temperature": "Temperatura", "Temporary Chat": "", "Text Splitter": "", "Text-to-Speech": "", "Text-to-Speech Engine": "Motor de Texto para Fala", "Thanks for your feedback!": "<PERSON><PERSON><PERSON> pelo seu feedback!", "The Application Account DN you bind with for search": "", "The base to search for users": "", "The batch size determines how many text requests are processed together at once. A higher batch size can increase the performance and speed of the model, but it also requires more memory.": "", "The developers behind this plugin are passionate volunteers from the community. If you find this plugin helpful, please consider contributing to its development.": "", "The evaluation leaderboard is based on the Elo rating system and is updated in real-time.": "", "The format to return a response in. Format can be json or a JSON schema.": "", "The height in pixels to compress images to. Leave empty for no compression.": "", "The language of the input audio. Supplying the input language in ISO-639-1 (e.g. en) format will improve accuracy and latency. Leave blank to automatically detect the language.": "", "The LDAP attribute that maps to the mail that users use to sign in.": "", "The LDAP attribute that maps to the username that users use to sign in.": "", "The leaderboard is currently in beta, and we may adjust the rating calculations as we refine the algorithm.": "", "The maximum file size in MB. If the file size exceeds this limit, the file will not be uploaded.": "", "The maximum number of files that can be used at once in chat. If the number of files exceeds this limit, the files will not be uploaded.": "", "The output format for the text. Can be 'json', 'markdown', or 'html'. Defaults to 'markdown'.": "", "The score should be a value between 0.0 (0%) and 1.0 (100%).": "A pontuação deve ser um valor entre 0.0 (0%) e 1.0 (100%).", "The temperature of the model. Increasing the temperature will make the model answer more creatively.": "", "The width in pixels to compress images to. Leave empty for no compression.": "", "Theme": "<PERSON><PERSON>", "Thinking...": "A pensar...", "This action cannot be undone. Do you wish to continue?": "", "This channel was created on {{createdAt}}. This is the very beginning of the {{channelName}} channel.": "", "This chat won't appear in history and your messages will not be saved.": "", "This ensures that your valuable conversations are securely saved to your backend database. Thank you!": "<PERSON><PERSON> garante que suas conversas valiosas sejam guardadas com segurança na sua base de dados de backend. Obrigado!", "This feature is experimental and may be modified or discontinued without notice.": "", "This is an experimental feature, it may not function as expected and is subject to change at any time.": "Isto é um recurso experimental, pode não funcionar conforme o esperado e está sujeito a alterações a qualquer momento.", "This model is not publicly available. Please select another model.": "", "This option controls how long the model will stay loaded into memory following the request (default: 5m)": "", "This option controls how many tokens are preserved when refreshing the context. For example, if set to 2, the last 2 tokens of the conversation context will be retained. Preserving context can help maintain the continuity of a conversation, but it may reduce the ability to respond to new topics.": "", "This option enables or disables the use of the reasoning feature in Ollama, which allows the model to think before generating a response. When enabled, the model can take a moment to process the conversation context and generate a more thoughtful response.": "", "This option sets the maximum number of tokens the model can generate in its response. Increasing this limit allows the model to provide longer answers, but it may also increase the likelihood of unhelpful or irrelevant content being generated.": "", "This option will delete all existing files in the collection and replace them with newly uploaded files.": "", "This response was generated by \"{{model}}\"": "", "This will delete": "", "This will delete <strong>{{NAME}}</strong> and <strong>all its contents</strong>.": "", "This will delete all models including custom models": "", "This will delete all models including custom models and cannot be undone.": "", "This will reset the knowledge base and sync all files. Do you wish to continue?": "", "Thorough explanation": "Explicação Minuciosa", "Thought for {{DURATION}}": "", "Thought for {{DURATION}} seconds": "", "Tika": "", "Tika Server URL required.": "", "Tiktoken": "", "Tip: Update multiple variable slots consecutively by pressing the tab key in the chat input after each replacement.": "Dica: Atualize vários slots de variáveis consecutivamente pressionando a tecla Tab na entrada da conversa após cada substituição.", "Title": "<PERSON><PERSON><PERSON><PERSON>", "Title (e.g. Tell me a fun fact)": "<PERSON><PERSON><PERSON><PERSON> (ex.: Diz-me um facto divertido)", "Title Auto-Generation": "Geração Automática de Título", "Title cannot be an empty string.": "T<PERSON><PERSON>lo não pode ser uma string vazia.", "Title Generation": "", "Title Generation Prompt": "Prompt de Geração de Título", "TLS": "", "To access the available model names for downloading,": "Para aceder aos nomes de modelo disponíveis para descarregar,", "To access the GGUF models available for downloading,": "Para aceder aos modelos GGUF disponíveis para descarregar,", "To access the WebUI, please reach out to the administrator. Admins can manage user statuses from the Admin Panel.": "Para aceder ao WebUI, entre em contato com o administrador. Os administradores podem gerir o status dos utilizadores no Painel de Administração.", "To attach knowledge base here, add them to the \"Knowledge\" workspace first.": "", "To learn more about available endpoints, visit our documentation.": "", "To protect your privacy, only ratings, model IDs, tags, and metadata are shared from your feedback—your chat logs remain private and are not included.": "", "To select actions here, add them to the \"Functions\" workspace first.": "", "To select filters here, add them to the \"Functions\" workspace first.": "", "To select toolkits here, add them to the \"Tools\" workspace first.": "", "Toast notifications for new updates": "", "Today": "Hoje", "Toggle search": "", "Toggle settings": "Alternar configurações", "Toggle sidebar": "Alternar barra lateral", "Toggle whether current connection is active.": "", "Token": "", "Too verbose": "", "Tool created successfully": "", "Tool deleted successfully": "", "Tool Description": "", "Tool ID": "", "Tool imported successfully": "", "Tool Name": "", "Tool Servers": "", "Tool updated successfully": "", "Tools": "", "Tools Access": "", "Tools are a function calling system with arbitrary code execution": "", "Tools Function Calling Prompt": "", "Tools have a function calling system that allows arbitrary code execution.": "", "Tools Public Sharing": "", "Top K": "Top K", "Top K Reranker": "", "Transformers": "", "Trouble accessing Ollama?": "Problemas a aceder ao Ollama?", "Trust Proxy Environment": "", "TTS Model": "Modelo TTS", "TTS Settings": "Configurações TTS", "TTS Voice": "Voz TTS", "Type": "Tipo", "Type Hugging Face Resolve (Download) URL": "Escreva o URL do Hugging Face Resolve (Descarregar)", "Uh-oh! There was an issue with the response.": "", "UI": "", "Unarchive All": "", "Unarchive All Archived Chats": "", "Unarchive Chat": "", "Underline": "", "Unloads {{FROM_NOW}}": "", "Unlock mysteries": "", "Unpin": "", "Unravel secrets": "", "Untagged": "", "Untitled": "", "Update": "", "Update and Copy Link": "Atualizar e Copiar Link", "Update for the latest features and improvements.": "", "Update password": "<PERSON><PERSON><PERSON><PERSON>", "Updated": "", "Updated at": "", "Updated At": "", "Upgrade to a licensed plan for enhanced capabilities, including custom theming and branding, and dedicated support.": "", "Upload": "", "Upload a GGUF model": "Carregar um modelo GGUF", "Upload Audio": "", "Upload directory": "", "Upload files": "", "Upload Files": "<PERSON><PERSON><PERSON>", "Upload Pipeline": "<PERSON>egar <PERSON>", "Upload Progress": "Progresso do Carregamento", "URL": "", "URL Mode": "Modo de URL", "Usage": "", "Use '#' in the prompt input to load and include your knowledge.": "", "Use Gravatar": "<PERSON><PERSON>", "Use groups to group your users and assign permissions.": "", "Use Initials": "Usar Iniciais", "Use LLM": "", "Use no proxy to fetch page contents.": "", "Use proxy designated by http_proxy and https_proxy environment variables to fetch page contents.": "", "user": "utilizador", "User": "", "User location successfully retrieved.": "", "User menu": "", "User Webhooks": "", "Username": "", "Users": "Utilizadores", "Using the default arena model with all models. Click the plus button to add custom models.": "", "Utilize": "<PERSON><PERSON><PERSON><PERSON>", "Valid time units:": "Unidades de tempo válidas:", "Valves": "", "Valves updated": "", "Valves updated successfully": "", "variable": "<PERSON><PERSON><PERSON><PERSON>", "variable to have them replaced with clipboard content.": "variável para que sejam substituídos pelo conteúdo da área de transferência.", "Verify Connection": "", "Verify SSL Certificate": "", "Version": "Vers<PERSON>", "Version {{selectedVersion}} of {{totalVersions}}": "", "View Replies": "", "View Result from **{{NAME}}**": "", "Visibility": "", "Vision": "", "Voice": "", "Voice Input": "", "Voice mode": "", "Warning": "Aviso", "Warning:": "", "Warning: Enabling this will allow users to upload arbitrary code on the server.": "", "Warning: If you update or change your embedding model, you will need to re-import all documents.": "Aviso: Se você atualizar ou alterar o seu modelo de vetorização, você tem de reimportar todos os documentos.", "Warning: Jupyter execution enables arbitrary code execution, posing severe security risks—proceed with extreme caution.": "", "Web": "Web", "Web API": "Web API", "Web Loader Engine": "", "Web Search": "Pesquisa na Web", "Web Search Engine": "Motor de Pesquisa Web", "Web Search in Chat": "", "Web Search Query Generation": "", "Webhook URL": "URL do Webhook", "WebUI Settings": "Configurações WebUI", "WebUI URL": "", "WebUI will make requests to \"{{url}}\"": "", "WebUI will make requests to \"{{url}}/api/chat\"": "", "WebUI will make requests to \"{{url}}/chat/completions\"": "", "Weight of BM25 Retrieval": "", "What are you trying to achieve?": "", "What are you working on?": "", "What's New in": "O que há de novo em", "When enabled, the model will respond to each chat message in real-time, generating a response as soon as the user sends a message. This mode is useful for live chat applications, but may impact performance on slower hardware.": "", "wherever you are": "", "Whether to paginate the output. Each page will be separated by a horizontal rule and page number. Defaults to False.": "", "Whisper (Local)": "<PERSON><PERSON><PERSON> (Local)", "Why?": "", "Widescreen Mode": "Modo Widescreen", "Won": "", "Works together with top-k. A higher value (e.g., 0.95) will lead to more diverse text, while a lower value (e.g., 0.5) will generate more focused and conservative text.": "", "Workspace": "Espaço de Trabalho", "Workspace Permissions": "", "Write": "", "Write a prompt suggestion (e.g. Who are you?)": "Escreva uma sugestão de prompt (por exemplo, Quem és tu?)", "Write a summary in 50 words that summarizes [topic or keyword].": "Escreva um resumo em 50 palavras que resuma [tópico ou palavra-chave].", "Write something...": "", "Yacy Instance URL": "", "Yacy Password": "", "Yacy Username": "", "Yesterday": "Ontem", "You": "Você", "You are currently using a trial license. Please contact support to upgrade your license.": "", "You can only chat with a maximum of {{maxCount}} file(s) at a time.": "", "You can personalize your interactions with LLMs by adding memories through the 'Manage' button below, making them more helpful and tailored to you.": "Você pode personalizar as suas interações com LLMs adicionando memórias através do botão 'Gerir' abaixo, tornando-as mais <PERSON> e personalizadas para você.", "You cannot upload an empty file.": "", "You do not have permission to upload files.": "", "You have no archived conversations.": "Você não tem conversas arquivadas.", "You have shared this chat": "Você partilhou esta conversa", "You're a helpful assistant.": "Você é um assistente útil.", "You're now logged in.": "Você agora está conectado.", "Your account status is currently pending activation.": "O status da sua conta está atualmente com a ativação pendente.", "Your entire contribution will go directly to the plugin developer; Open WebUI does not take any percentage. However, the chosen funding platform might have its own fees.": "", "Youtube": "Youtube", "Youtube Language": "", "Youtube Proxy URL": ""}