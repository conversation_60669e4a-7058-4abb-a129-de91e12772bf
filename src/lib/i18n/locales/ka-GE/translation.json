{"-1 for no limit, or a positive integer for a specific limit": "-1 ლიმიტის გამოსართავად, ან დადებითი მთელი რიცხვი კონკრეტული ლიმიტისთვის", "'s', 'm', 'h', 'd', 'w' or '-1' for no expiration.": "'s', 'm', 'h', 'd', 'w' ან '-1' - უვადოსთვის.", "(e.g. `sh webui.sh --api --api-auth username_password`)": "(მაგ: `sh webui.sh --api --api-auth username_password`)", "(e.g. `sh webui.sh --api`)": "(მაგ: `sh webui.sh --api`)", "(latest)": "(უახლესი)", "(leave blank for to use commercial endpoint)": "", "[Last] dddd [at] h:mm A": "", "[Today at] h:mm A": "", "[Yesterday at] h:mm A": "", "{{ models }}": "{{ მოდელები }}", "{{COUNT}} Available Tools": "", "{{COUNT}} characters": "", "{{COUNT}} hidden lines": "", "{{COUNT}} Replies": "{{COUNT}} პასუხი", "{{COUNT}} words": "", "{{user}}'s Chats": "{{user}}-ის ჩათები", "{{webUIName}} Backend Required": "{{webUIName}} საჭიროა უკანაბოლო", "*Prompt node ID(s) are required for image generation": "", "A new version (v{{LATEST_VERSION}}) is now available.": "ხელმისაწვდომია ახალი ვერსია (v{{LATEST_VERSION}}).", "A task model is used when performing tasks such as generating titles for chats and web search queries": "დავალების მოდელი გამოიყენება ისეთი ამოცანების შესრულებისას, როგორიცაა ჩეთების სათაურების გენერირება და ვებ – ძიების მოთხოვნები", "a user": "მომხმარებელი", "About": "შესახებ", "Accept autocomplete generation / Jump to prompt variable": "", "Access": "წვდომა", "Access Control": "წვდომის კონტროლი", "Accessible to all users": "ხელმისაწვდომია ყველა მომხმარებლისთვის", "Account": "ანგარიში", "Account Activation Pending": "დარჩენილი ანგარიშის აქტივაცია", "Accurate information": "სწორი ინფორმაცია", "Action": "", "Actions": "ქმედებები", "Activate": "აქტივაცია", "Activate this command by typing \"/{{COMMAND}}\" to chat input.": "", "Active Users": "აქტიური მომხმარებლები", "Add": "დამატება", "Add a model ID": "მოდელის ID-ის დამატება", "Add a short description about what this model does": "დაამატეთ მოკლე აღწერა იმის შესახებ, თუ რას აკეთებს ეს მოდელი", "Add a tag": "ჭდის დამატება", "Add Arena Model": "არენის მოდელის დამატება", "Add Connection": "შეერთების დამატება", "Add Content": "შემცველობის დამატება", "Add content here": "შემცველობის აქ დამატება", "Add Custom Parameter": "", "Add custom prompt": "მორგებული მოთხოვნის დამატება", "Add Files": "ფაილების დამატება", "Add Group": "ჯგუფის დამატება", "Add Memory": "მეხსიერების დამატება", "Add Model": "მოდელის დამატება", "Add Reaction": "რეაქციის დამატება", "Add Tag": "ჭდის დამატება", "Add Tags": "ჭდეების დამატება", "Add text content": "ტექსტური შემცველობის დამატება", "Add User": "მომხმარებლის დამატება", "Add User Group": "მომხმარებლის ჯგუფის დამატება", "Adjusting these settings will apply changes universally to all users.": "ამ პარამეტრების რეგულირება ცვლილებებს უნივერსალურად გამოიყენებს ყველა მომხმარებლისთვის.", "admin": "ადმინისტრატორი", "Admin": "ადმინი", "Admin Panel": "ადმინისტრატორის პანელი", "Admin Settings": "ადმინისტრატორის მორგება", "Admins have access to all tools at all times; users need tools assigned per model in the workspace.": "", "Advanced Parameters": "დამატებითი პარამეტრები", "Advanced Params": "დამატებითი პარამეტრები", "AI": "", "All": "", "All Documents": "ყველა დოკუმენტი", "All models deleted successfully": "ყველა მოდელი წარმატებით წაიშალა", "Allow Call": "", "Allow Chat Controls": "ჩატის კონტროლის ელემენტების დაშვება", "Allow Chat Delete": "ჩატის წაშლის დაშვება", "Allow Chat Deletion": "ჩატის წაშლის დაშვება", "Allow Chat Edit": "ჩატის ჩასწორების დაშვება", "Allow Chat Export": "", "Allow Chat Share": "", "Allow Chat System Prompt": "", "Allow File Upload": "ფაილის ატვირთვის დაშვება", "Allow Multiple Models in Chat": "", "Allow non-local voices": "არალოკალური ხმების დაშვება", "Allow Speech to Text": "", "Allow Temporary Chat": "დროებითი ჩატის დაშვება", "Allow Text to Speech": "", "Allow User Location": "მომხმარებლის მდებარეობის დაშვება", "Allow Voice Interruption in Call": "", "Allowed Endpoints": "დაშვებული ბოლოწერტილები", "Allowed File Extensions": "", "Allowed file extensions for upload. Separate multiple extensions with commas. Leave empty for all file types.": "", "Already have an account?": "უკვე გაქვთ ანგარიში?", "Alternative to the top_p, and aims to ensure a balance of quality and variety. The parameter p represents the minimum probability for a token to be considered, relative to the probability of the most likely token. For example, with p=0.05 and the most likely token having a probability of 0.9, logits with a value less than 0.045 are filtered out.": "", "Always": "ყოველთვის", "Always Collapse Code Blocks": "", "Always Expand Details": "", "Always Play Notification Sound": "", "Amazing": "გადასარევია", "an assistant": "დამხმარე", "Analyzed": "გაანაზლიებულია", "Analyzing...": "ანალიზი...", "and": "და", "and {{COUNT}} more": "და კიდევ {{COUNT}}", "and create a new shared link.": "და ახალი გაზიარებული ბმულის შექმნა.", "Android": "", "API": "", "API Base URL": "API-ის საბაზისო URL", "API details for using a vision-language model in the picture description. This parameter is mutually exclusive with picture_description_local.": "", "API Key": "API გასაღები", "API Key created.": "API გასაღები შეიქმნა.", "API Key Endpoint Restrictions": "", "API keys": "API გასაღებები", "API Version": "", "Application DN": "აპლიკაციის DN", "Application DN Password": "აპლიკაციის DN-ის პაროლი", "applies to all users with the \"user\" role": "", "April": "აპრილი", "Archive": "დაარქივება", "Archive All Chats": "ყველა ჩატის დაარქივება", "Archived Chats": "დაარქივებული ჩატები", "archived-chat-export": "", "Are you sure you want to clear all memories? This action cannot be undone.": "", "Are you sure you want to delete this channel?": "", "Are you sure you want to delete this message?": "", "Are you sure you want to unarchive all archived chats?": "", "Are you sure?": "დარწმუნებული ბრძანდებით?", "Arena Models": "არენის მოდელები", "Artifacts": "არტეფაქტები", "Ask": "", "Ask a question": "კითხვის დასმა", "Assistant": "დამხმარე", "Attach file from knowledge": "ფაილის მიმაგრება", "Attention to detail": "ყურადღებით დეტალებთან", "Attribute for Mail": "ატრიბუტი ფოსტისთვის", "Attribute for Username": "ატრიბუტი მომხმარებლის სახელისთვის", "Audio": "აუდიო", "August": "აგვისტო", "Auth": "", "Authenticate": "ავთენტიკაცია", "Authentication": "ავთენტიკაცია", "Auto": "", "Auto-Copy Response to Clipboard": "პასუხის ავტომატური კოპირება ბუფერში", "Auto-playback response": "ავტომატური დაკვრის პასუხი", "Autocomplete Generation": "", "Autocomplete Generation Input Max Length": "", "Automatic1111": "Automatic1111", "AUTOMATIC1111 Api Auth String": "", "AUTOMATIC1111 Base URL": "AUTOMATIC1111 საბაზისო მისამართი", "AUTOMATIC1111 Base URL is required.": "AUTOMATIC1111 საბაზისო მისამართი აუცილებელია.", "Available list": "ხელმისაწვდომი სია", "Available Tools": "", "available!": "ხელმისაწვდომია!", "Awful": "საშინელი", "Azure AI Speech": "", "Azure Region": "Azure-ის რეგიონი", "Back": "უკან", "Bad Response": "არასწორი პასუხი", "Banners": "ბანერები", "Base Model (From)": "საბაზისო მოდელი (საიდან)", "Base Model List Cache speeds up access by fetching base models only at startup or on settings save—faster, but may not show recent base model changes.": "", "before": "მითითებულ დრომდე", "Being lazy": "ზარმაცობა", "Beta": "ბეტა", "Bing Search V7 Endpoint": "", "Bing Search V7 Subscription Key": "", "Bocha Search API Key": "", "Bold": "", "Boosting or penalizing specific tokens for constrained responses. Bias values will be clamped between -100 and 100 (inclusive). (Default: none)": "", "Both Docling OCR Engine and Language(s) must be provided or both left empty.": "", "Brave Search API Key": "Brave Search API-ის გასაღები", "Bullet List": "", "By {{name}}": "ავტორი {{name}}", "Bypass Embedding and Retrieval": "", "Bypass Web Loader": "", "Cache Base Model List": "", "Calendar": "კალენდარი", "Call": "ზარი", "Call feature is not supported when using Web STT engine": "", "Camera": "კამერა", "Cancel": "გაუქმება", "Capabilities": "შესაძლებლობები", "Capture": "ჩაჭერა", "Capture Audio": "", "Certificate Path": "სერტიფიკატის ბილიკი", "Change Password": "პაროლის შეცვლა", "Channel Name": "არხის სახელი", "Channels": "არხები", "Character": "სიმბოლო", "Character limit for autocomplete generation input": "", "Chart new frontiers": "", "Chat": "ჩატი", "Chat Background Image": "", "Chat Bubble UI": "ჩატის ბუშტის ინტერფეისი", "Chat Controls": "", "Chat direction": "ჩატის მიმართულება", "Chat Overview": "", "Chat Permissions": "", "Chat Tags Auto-Generation": "", "Chats": "საუბრები", "Check Again": "თავიდან შემოწმება", "Check for updates": "განახლებების შემოწმება", "Checking for updates...": "განახლებების შემოწმება...", "Choose a model before saving...": "აირჩიეთ მოდელი შენახვამდე...", "Chunk Overlap": "ფრაგმენტის გადაფარვა", "Chunk Size": "ფრაგმენტის ზომა", "Ciphers": "", "Citation": "ციტატა", "Citations": "", "Clear memory": "", "Clear Memory": "", "click here": "აქ დააწკაპუნეთ", "Click here for filter guides.": "", "Click here for help.": "დახმარებისთვის დააწკაპუნეთ აქ.", "Click here to": "დააწკაპუნეთ აქ", "Click here to download user import template file.": "", "Click here to learn more about faster-whisper and see the available models.": "", "Click here to see available models.": "", "Click here to select": "ასარჩევად დააწკაპუნეთ აქ", "Click here to select a csv file.": "დააწკაპუნეთ აქ csv ფაილის ასარჩევად.", "Click here to select a py file.": "", "Click here to upload a workflow.json file.": "", "click here.": "დააწკაპუნეთ აქ.", "Click on the user role button to change a user's role.": "მომხმარებლის როლის შესაცვლელად დააწკაპუნეთ მომხმარებლის როლზე.", "Clipboard write permission denied. Please check your browser settings to grant the necessary access.": "", "Clone": "კლონი", "Clone Chat": "", "Clone of {{TITLE}}": "", "Close": "დახურვა", "Close Configure Connection Modal": "", "Close modal": "", "Close settings modal": "", "Code Block": "", "Code execution": "", "Code Execution": "", "Code Execution Engine": "", "Code Execution Timeout": "", "Code formatted successfully": "", "Code Interpreter": "", "Code Interpreter Engine": "", "Code Interpreter Prompt Template": "", "Collapse": "", "Collection": "კოლექცია", "Color": "ფერი", "ComfyUI": "ComfyUI", "ComfyUI API Key": "", "ComfyUI Base URL": "ComfyUI საბაზისო URL", "ComfyUI Base URL is required.": "ComfyUI საბაზისო URL აუცილებელია.", "ComfyUI Workflow": "", "ComfyUI Workflow Nodes": "", "Command": "ბრძანება", "Comment": "", "Completions": "", "Concurrent Requests": "ერთდროული მოთხოვნები", "Configure": "მორგება", "Confirm": "დადასტურება", "Confirm Password": "გაიმეორეთ პაროლი", "Confirm your action": "", "Confirm your new password": "", "Connect to your own OpenAI compatible API endpoints.": "", "Connect to your own OpenAPI compatible external tool servers.": "", "Connection failed": "", "Connection successful": "", "Connection Type": "", "Connections": "კავშირები", "Connections saved successfully": "", "Connections settings updated": "", "Constrains effort on reasoning for reasoning models. Only applicable to reasoning models from specific providers that support reasoning effort.": "", "Contact Admin for WebUI Access": "", "Content": "შემცველობა", "Content Extraction Engine": "", "Continue Response": "პასუხის გაგრძელება", "Continue with {{provider}}": "", "Continue with Email": "", "Continue with LDAP": "", "Control how message text is split for TTS requests. 'Punctuation' splits into sentences, 'paragraphs' splits into paragraphs, and 'none' keeps the message as a single string.": "", "Control the repetition of token sequences in the generated text. A higher value (e.g., 1.5) will penalize repetitions more strongly, while a lower value (e.g., 1.1) will be more lenient. At 1, it is disabled.": "", "Controls": "მმართველები", "Controls the balance between coherence and diversity of the output. A lower value will result in more focused and coherent text.": "", "Copied": "დაკოპირდა", "Copied link to clipboard": "", "Copied shared chat URL to clipboard!": "გაზიარებული ჩატის ბმული დაკოპირდა ბუფერში!", "Copied to clipboard": "დაკოპირდა გაცვლის ბაფერში", "Copy": "კოპირება", "Copy Formatted Text": "", "Copy last code block": "ბოლო კოდის ბლოკის კოპირება", "Copy last response": "ბოლო პასუხის კოპირება", "Copy link": "", "Copy Link": "ბმულის კოპირება", "Copy to clipboard": "ბუფერში კოპირება", "Copying to clipboard was successful!": "გაცვლის ბუფერში კოპირება წარმატებულია!", "CORS must be properly configured by the provider to allow requests from Open WebUI.": "", "Create": "შექმნა", "Create a knowledge base": "", "Create a model": "მოდელის შექმნა", "Create Account": "ანგარიშის შექმნა", "Create Admin Account": "", "Create Channel": "არხის შექმნა", "Create Group": "ჯგუფის შექმნა", "Create Knowledge": "", "Create new key": "ახალი გასაღების შექმნა", "Create new secret key": "ახალი საიდუმლო გასაღების შექმნა", "Create Note": "", "Create your first note by clicking on the plus button below.": "", "Created at": "შექმნის დრო", "Created At": "შექმნის დრო", "Created by": "ავტორი", "CSV Import": "CSV-ის შემოტანა", "Ctrl+Enter to Send": "", "Current Model": "მიმდინარე მოდელი", "Current Password": "მიმდინარე პაროლი", "Custom": "ხელით", "Custom description enabled": "", "Custom Parameter Name": "", "Custom Parameter Value": "", "Danger Zone": "", "Dark": "მუქი", "Database": "მონაცემთა ბაზა", "Datalab Marker API": "", "Datalab Marker API Key required.": "", "DD/MM/YYYY": "", "December": "დეკემბერი", "Default": "ნაგულისხმევი", "Default (Open AI)": "", "Default (SentenceTransformers)": "ნაგულისხმევი (SentenceTransformers)", "Default description enabled": "", "Default mode works with a wider range of models by calling tools once before execution. Native mode leverages the model's built-in tool-calling capabilities, but requires the model to inherently support this feature.": "", "Default Model": "ნაგულისხმევი მოდელი", "Default model updated": "ნაგულისხმევი მოდელი განახლდა", "Default Models": "", "Default permissions": "ნაგულისხმები წვდომები", "Default permissions updated successfully": "", "Default Prompt Suggestions": "ნაგულისხმევი მოთხოვნის მინიშნებები", "Default to 389 or 636 if TLS is enabled": "", "Default to ALL": "", "Default to segmented retrieval for focused and relevant content extraction, this is recommended for most cases.": "", "Default User Role": "მომხმარებლის ნაგულისხმევი როლი", "Delete": "წაშლა", "Delete a model": "მოდელის წაშლა", "Delete All Chats": "ყველა ჩატის წაშლა", "Delete All Models": "", "Delete chat": "საუბრის წაშლა", "Delete Chat": "საუბრის წაშლა", "Delete chat?": "წავშალო ჩატი?", "Delete folder?": "წავშალო საქაღალდეები?", "Delete function?": "წავშალო ფუნქცია?", "Delete Message": "შეტყობინების წაშლა", "Delete message?": "წავშალო შეტყობინება?", "Delete note?": "", "Delete prompt?": "წავშალო მოთხოვნის ზოლი?", "delete this link": "ამ ბმული წაშლა", "Delete tool?": "წავშალო ხელსაწყო?", "Delete User": "მომხმარებლის წაშლა", "Deleted {{deleteModelTag}}": "{{deleteModelTag}} წაშლილია", "Deleted {{name}}": "Deleted {{name}}", "Deleted User": "წაშლილი მომხმარებელი", "Deployment names are required for Azure OpenAI": "", "Describe Pictures in Documents": "", "Describe your knowledge base and objectives": "", "Description": "აღწერა", "Detect Artifacts Automatically": "", "Dictate": "", "Didn't fully follow instructions": "ინსტრუქციებს სრულად არ მივყევი", "Direct": "", "Direct Connections": "პირდაპირი მიერთება", "Direct Connections allow users to connect to their own OpenAI compatible API endpoints.": "", "Direct Tool Servers": "", "Disable Code Interpreter": "", "Disable Image Extraction": "", "Disable image extraction from the PDF. If Use LLM is enabled, images will be automatically captioned. Defaults to False.": "", "Disabled": "გამორთული", "Discover a function": "", "Discover a model": "აღმოაჩინეთ მოდელი", "Discover a prompt": "აღმოაჩინეთ მოთხოვნა", "Discover a tool": "", "Discover how to use Open WebUI and seek support from the community.": "", "Discover wonders": "", "Discover, download, and explore custom functions": "", "Discover, download, and explore custom prompts": "აღმოაჩინეთ, გადმოწერეთ და შეისწავლეთ მორგებული მოთხოვნები", "Discover, download, and explore custom tools": "", "Discover, download, and explore model presets": "აღმოაჩინეთ, გადმოწერეთ და შეისწავლეთ მოდელის პარამეტრები", "Display": "ჩვენება", "Display Emoji in Call": "", "Display the username instead of You in the Chat": "ჩატში თქვენს მაგიერ მომხმარებლის სახელის ჩვენება", "Displays citations in the response": "", "Dive into knowledge": "", "Do not install functions from sources you do not fully trust.": "", "Do not install tools from sources you do not fully trust.": "", "Docling": "", "Docling Server URL required.": "", "Document": "დოკუმენტი", "Document Intelligence": "", "Document Intelligence endpoint and key required.": "", "Documentation": "დოკუმენტაცია", "Documents": "დოკუმენტები", "does not make any external connections, and your data stays securely on your locally hosted server.": "არ ამყარებს გარე კავშირებს და თქვენი მონაცემები უსაფრთხოდ რჩება თქვენს ლოკალურ სერვერზე.", "Domain Filter List": "", "Don't have an account?": "არ გაქვთ ანგარიში?", "don't install random functions from sources you don't trust.": "", "don't install random tools from sources you don't trust.": "", "Don't like the style": "არ მომწონს სტილი", "Done": "დასრულებული", "Download": "გადმოწერა", "Download as SVG": "", "Download canceled": "გადმოწერა გაუქმდა", "Download Database": "მონაცემთა ბაზის გადმოწერა", "Drag and drop a file to upload or select a file to view": "", "Draw": "ხატვა", "Drop any files here to upload": "", "e.g. '30s','10m'. Valid time units are 's', 'm', 'h'.": "მაგ: '30წ', '10მ'. მოქმედი დროის ერთეულები: 'წ', 'წთ', 'სთ'.", "e.g. \"json\" or a JSON schema": "", "e.g. 60": "მაგ: 60", "e.g. A filter to remove profanity from text": "", "e.g. en": "", "e.g. My Filter": "მაგ: ჩემი ფილტრი", "e.g. My Tools": "მაგ: ჩემი ხელსაწყოები", "e.g. my_filter": "მაგ: ჩემი_ფილტრი", "e.g. my_tools": "მაგ: ჩემი_ხელსაწყოები", "e.g. pdf, docx, txt": "", "e.g. Tools for performing various operations": "", "e.g., 3, 4, 5 (leave blank for default)": "", "e.g., audio/wav,audio/mpeg,video/* (leave blank for defaults, * for all)": "", "e.g., en-US,ja-JP (leave blank for auto-detect)": "", "e.g., westus (leave blank for eastus)": "", "e.g.) en,fr,de": "", "Edit": "ჩასწორება", "Edit Arena Model": "არენის მოდელის ჩასწორება", "Edit Channel": "არხის ჩასწორება", "Edit Connection": "შეერთების ჩასწორება", "Edit Default Permissions": "ნაგულისხმევი წვდომების ჩასწორება", "Edit Folder": "", "Edit Memory": "მეხსიერების ჩასწორება", "Edit User": "მომხმარებლის ჩასწორება", "Edit User Group": "მომხმარებლის ჯგუფის ჩასწორება", "Edited": "", "Editing": "", "Eject": "", "ElevenLabs": "ElevenLabs", "Email": "ელფოსტა", "Embark on adventures": "", "Embedding": "", "Embedding Batch Size": "", "Embedding Model": "მოდელის ჩაშენება", "Embedding Model Engine": "ჩაშენებული მოდელის ძრავა", "Embedding model set to \"{{embedding_model}}\"": "ჩაშენებული მოდელი დაყენებულია მნიშვნელობაზე \"{{embedding_model}}\"", "Enable API Key": "", "Enable autocomplete generation for chat messages": "", "Enable Code Execution": "", "Enable Code Interpreter": "", "Enable Community Sharing": "საზოგადოების გაზიარების ჩართვა", "Enable Memory Locking (mlock) to prevent model data from being swapped out of RAM. This option locks the model's working set of pages into RAM, ensuring that they will not be swapped out to disk. This can help maintain performance by avoiding page faults and ensuring fast data access.": "", "Enable Memory Mapping (mmap) to load model data. This option allows the system to use disk storage as an extension of RAM by treating disk files as if they were in RAM. This can improve model performance by allowing for faster data access. However, it may not work correctly with all systems and can consume a significant amount of disk space.": "", "Enable Message Rating": "", "Enable Mirostat sampling for controlling perplexity.": "", "Enable New Sign Ups": "ახალი რეგისტრაციების ჩართვა", "Enabled": "ჩართულია", "Endpoint URL": "", "Enforce Temporary Chat": "", "Enhance": "", "Ensure your CSV file includes 4 columns in this order: Name, Email, Password, Role.": "დარწმუნდით, რომ თქვენი CSV-ფაილი შეიცავს 4 ველს ამ მიმდევრობით: სახელი, ელფოსტა, პაროლი, როლი.", "Enter {{role}} message here": "შეიყვანე {{role}} შეტყობინება აქ", "Enter a detail about yourself for your LLMs to recall": "შეიყვანეთ რამე თქვენს შესახებ, რომ თქვენმა LLM-მა გაიხსენოს", "Enter a title for the pending user info overlay. Leave empty for default.": "", "Enter a watermark for the response. Leave empty for none.": "", "Enter api auth string (e.g. username:password)": "", "Enter Application DN": "", "Enter Application DN Password": "", "Enter Bing Search V7 Endpoint": "", "Enter Bing Search V7 Subscription Key": "", "Enter BM25 Weight": "", "Enter Bocha Search API Key": "", "Enter Brave Search API Key": "შეიყვანეთ Brave Search API-ის გასაღები", "Enter certificate path": "", "Enter CFG Scale (e.g. 7.0)": "", "Enter Chunk Overlap": "შეიყვანეთ ფრაგმენტის გადაფარვა", "Enter Chunk Size": "შეიყვანე ფრაგმენტის ზომა", "Enter comma-separated \"token:bias_value\" pairs (example: 5432:100, 413:-100)": "", "Enter Config in JSON format": "", "Enter content for the pending user info overlay. Leave empty for default.": "", "Enter Datalab Marker API Key": "", "Enter description": "შეიყვანეთ აღწერა", "Enter Docling OCR Engine": "", "Enter Docling OCR Language(s)": "", "Enter Docling Server URL": "", "Enter Document Intelligence Endpoint": "", "Enter Document Intelligence Key": "", "Enter domains separated by commas (e.g., example.com,site.org)": "", "Enter Exa API Key": "", "Enter External Document Loader API Key": "", "Enter External Document Loader URL": "", "Enter External Web Loader API Key": "", "Enter External Web Loader URL": "", "Enter External Web Search API Key": "", "Enter External Web Search URL": "", "Enter Firecrawl API Base URL": "", "Enter Firecrawl API Key": "", "Enter folder name": "", "Enter Github Raw URL": "შეიყვანეთ Github Raw URL", "Enter Google PSE API Key": "შეიყვანეთ Google PSE API-ის გასაღები", "Enter Google PSE Engine Id": "შეიყვანეთ Google PSE ძრავის ID", "Enter Image Size (e.g. 512x512)": "შეიყვანეთ სურათის ზომა (მაგ. 512x512)", "Enter Jina API Key": "", "Enter Jupyter Password": "", "Enter Jupyter Token": "", "Enter Jupyter URL": "", "Enter Kagi Search API Key": "", "Enter Key Behavior": "", "Enter language codes": "შეიყვანეთ ენის კოდები", "Enter Mistral API Key": "", "Enter Model ID": "", "Enter model tag (e.g. {{modelTag}})": "შეიყვანეთ მოდელის ჭდე (მაგ: {{modelTag}})", "Enter Mojeek Search API Key": "", "Enter name": "", "Enter New Password": "", "Enter Number of Steps (e.g. 50)": "შეიყვანეთ ნაბიჯების რაოდენობა (მაგ. 50)", "Enter Perplexity API Key": "", "Enter Playwright Timeout": "", "Enter Playwright WebSocket URL": "", "Enter proxy URL (e.g. **************************:port)": "", "Enter reasoning effort": "", "Enter Sampler (e.g. Euler a)": "", "Enter Scheduler (e.g. Karras)": "", "Enter Score": "შეიყვანეთ ქულა", "Enter SearchApi API Key": "", "Enter SearchApi Engine": "", "Enter Searxng Query URL": "შეიყვანეთ Searxng Query URL", "Enter Seed": "", "Enter SerpApi API Key": "", "Enter SerpApi Engine": "", "Enter Serper API Key": "შეიყვანეთ Serper API Key", "Enter Serply API Key": "", "Enter Serpstack API Key": "შეიყვანეთ Serpstack API Key", "Enter server host": "", "Enter server label": "", "Enter server port": "", "Enter Sougou Search API sID": "", "Enter Sougou Search API SK": "", "Enter stop sequence": "შეიყვანეთ გაჩერების მიმდევრობა", "Enter system prompt": "", "Enter system prompt here": "", "Enter Tavily API Key": "", "Enter Tavily Extract Depth": "", "Enter the public URL of your WebUI. This URL will be used to generate links in the notifications.": "", "Enter the URL of the function to import": "", "Enter the URL to import": "", "Enter Tika Server URL": "", "Enter timeout in seconds": "", "Enter to Send": "", "Enter Top K": "შეიყვანეთ Top K", "Enter Top K Reranker": "", "Enter URL (e.g. http://127.0.0.1:7860/)": "შეიყვანეთ ბმული (მაგ: http://127.0.0.1:7860/)", "Enter URL (e.g. http://localhost:11434)": "შეიყვანეთ ბმული (მაგ: http://localhost:11434)", "Enter Yacy Password": "", "Enter Yacy URL (e.g. http://yacy.example.com:8090)": "", "Enter Yacy Username": "", "Enter your current password": "შეიყვანეთ თქვენი მიმდინარე პაროლი", "Enter Your Email": "შეიყვანეთ თქვენი ელფოსტა", "Enter Your Full Name": "შეიყვანეთ თქვენი სრული სახელი", "Enter your message": "", "Enter your name": "", "Enter Your Name": "", "Enter your new password": "შეიყვანეთ თქვენი ახალი პაროლი", "Enter Your Password": "შეიყვანეთ თქვენი პაროლი", "Enter Your Role": "შეიყვანეთ თქვენი როლი", "Enter Your Username": "", "Enter your webhook URL": "", "Error": "შეცდომა", "ERROR": "ERROR", "Error accessing Google Drive: {{error}}": "", "Error accessing media devices.": "", "Error starting recording.": "", "Error unloading model: {{error}}": "", "Error uploading file: {{error}}": "", "Evaluations": "", "Everyone": "", "Exa API Key": "", "Example: (&(objectClass=inetOrgPerson)(uid=%s))": "", "Example: ALL": "", "Example: mail": "", "Example: ou=users,dc=foo,dc=example": "", "Example: sAMAccountName or uid or userPrincipalName": "", "Exceeded the number of seats in your license. Please contact support to increase the number of seats.": "", "Exclude": "გამორიცხვა", "Execute code for analysis": "", "Executing **{{NAME}}**...": "", "Expand": "", "Experimental": "ექსპერიმენტული", "Explain": "", "Explore the cosmos": "", "Export": "გატანა", "Export All Archived Chats": "", "Export All Chats (All Users)": "ყველა ჩატის გატანა (ყველა მომხმარებელი)", "Export chat (.json)": "", "Export Chats": "ჩატების გატანა", "Export Config to JSON File": "", "Export Functions": "", "Export Models": "მოდელების გატანა", "Export Presets": "პრესეტების გატანა", "Export Prompt Suggestions": "", "Export Prompts": "მოთხოვნების გატანა", "Export to CSV": "CVS-ში გატანა", "Export Tools": "", "External": "", "External Document Loader URL required.": "", "External Task Model": "", "External Web Loader API Key": "", "External Web Loader URL": "", "External Web Search API Key": "", "External Web Search URL": "", "Failed to add file.": "ფაილის დამატების შეცდომა.", "Failed to connect to {{URL}} OpenAPI tool server": "", "Failed to copy link": "", "Failed to create API Key.": "API-ის გასაღების შექმნა ჩავარდა.", "Failed to delete note": "", "Failed to extract content from the file: {{error}}": "", "Failed to extract content from the file.": "", "Failed to fetch models": "", "Failed to generate title": "", "Failed to load file content.": "", "Failed to read clipboard contents": "ბუფერის შემცველობის წაკითხვა ჩავარდა", "Failed to save connections": "", "Failed to save models configuration": "", "Failed to update settings": "", "Failed to upload file.": "", "Features": "მახასიათებლები", "Features Permissions": "", "February": "თებერვალი", "Feedback Details": "", "Feedback History": "", "Feedbacks": "", "Feel free to add specific details": "სპეციფიკური დეტალების დამატება პრობლემა არაა", "File": "ფაილი", "File added successfully.": "", "File content updated successfully.": "", "File Mode": "ფაილის რეჟიმი", "File not found.": "ფაილი ნაპოვნი არაა.", "File removed successfully.": "", "File size should not exceed {{maxSize}} MB.": "", "File Upload": "", "File uploaded successfully": "", "Files": "ფაილი", "Filter": "", "Filter is now globally disabled": "", "Filter is now globally enabled": "", "Filters": "ფილტრები", "Fingerprint spoofing detected: Unable to use initials as avatar. Defaulting to default profile image.": "აღმოჩენილია ანაბეჭდის გაყალბება: ინიციალების გამოყენება ავატარად შეუძლებელია. გამოყენებული იქნეა ნაგულისხმევი პროფილის სურათი.", "Firecrawl API Base URL": "", "Firecrawl API Key": "", "Fluidly stream large external response chunks": "დიდი გარე პასუხის ფრაგმენტების გლუვად დასტრიმვა", "Focus chat input": "ჩატში შეყვანის ფოკუსი", "Folder deleted successfully": "", "Folder Name": "", "Folder name cannot be empty.": "", "Folder name updated successfully": "", "Folder updated successfully": "", "Follow up": "", "Follow Up Generation": "", "Follow Up Generation Prompt": "", "Follow-Up Auto-Generation": "", "Followed instructions perfectly": "ინსტრუქციების ზუსტად მიჰყევით", "Force OCR": "", "Force OCR on all pages of the PDF. This can lead to worse results if you have good text in your PDFs. Defaults to False.": "", "Forge new paths": "", "Form": "ფორმა", "Format your variables using brackets like this:": "", "Forwards system user session credentials to authenticate": "", "Full Context Mode": "", "Function": "ფუნქცია", "Function Calling": "", "Function created successfully": "", "Function deleted successfully": "", "Function Description": "", "Function ID": "", "Function imported successfully": "", "Function is now globally disabled": "", "Function is now globally enabled": "", "Function Name": "", "Function updated successfully": "", "Functions": "ფუნქციები", "Functions allow arbitrary code execution.": "", "Functions imported successfully": "", "Gemini": "მარჩბივი", "Gemini API Config": "", "Gemini API Key is required.": "", "General": "ზოგადი", "Generate": "", "Generate an image": "", "Generate Image": "", "Generate prompt pair": "", "Generating search query": "ძებნის მოთხოვნის გენერაცია", "Generating...": "", "Get information on {{name}} in the UI": "", "Get started": "დაიწყეთ", "Get started with {{WEBUI_NAME}}": "", "Global": "გლობალური", "Good Response": "კარგი პასუხი", "Google Drive": "Google Drive", "Google PSE API Key": "Google PSE API გასაღები", "Google PSE Engine Id": "Google PSE ძრავის Id", "Group created successfully": "", "Group deleted successfully": "", "Group Description": "", "Group Name": "ჯგუფის სახელი", "Group updated successfully": "", "Groups": "ჯგუფები", "H1": "", "H2": "", "H3": "", "Haptic Feedback": "", "Hello, {{name}}": "გამარჯობა, {{name}}", "Help": "დახმარება", "Help us create the best community leaderboard by sharing your feedback history!": "", "Hex Color": "თექვსმეტობითი ფერი", "Hex Color - Leave empty for default color": "", "Hide": "დამალვა", "Hide from Sidebar": "", "Hide Model": "", "High Contrast Mode": "", "Home": "მთავარი", "Host": "ჰოსტი", "How can I help you today?": "რით შემიძლია დაგეხმაროთ დღეს?", "How would you rate this response?": "", "HTML": "", "Hybrid Search": "ჰიბრიდური ძებნა", "I acknowledge that I have read and I understand the implications of my action. I am aware of the risks associated with executing arbitrary code and I have verified the trustworthiness of the source.": "", "ID": "ID", "iframe Sandbox Allow Forms": "", "iframe Sandbox Allow Same Origin": "", "Ignite curiosity": "", "Image": "გამოსახულება", "Image Compression": "", "Image Compression Height": "", "Image Compression Width": "", "Image Generation": "გამოსახულების გენერაცია", "Image Generation (Experimental)": "გამოსახულებების გენერაცია (ექსპერიმენტული)", "Image Generation Engine": "გამოსახულებების გენერაციის ძრავა", "Image Max Compression Size": "", "Image Max Compression Size height": "", "Image Max Compression Size width": "", "Image Prompt Generation": "", "Image Prompt Generation Prompt": "", "Image Settings": "გამოსახულების პარამეტრები", "Images": "გამოსახულებები", "Import": "", "Import Chats": "ჩატების შემოტანა", "Import Config from JSON File": "", "Import From Link": "", "Import Functions": "", "Import Models": "მოდელების შემოტანა", "Import Notes": "", "Import Presets": "პრესეტების შემოტანა", "Import Prompt Suggestions": "", "Import Prompts": "მოთხოვნების შემოტანა", "Import Tools": "", "Include": "ჩართვა", "Include `--api-auth` flag when running stable-diffusion-webui": "", "Include `--api` flag when running stable-diffusion-webui": "`--api` ალმის ჩასმა stable-diffusion-webui-ის გამოყენებისას", "Influences how quickly the algorithm responds to feedback from the generated text. A lower learning rate will result in slower adjustments, while a higher learning rate will make the algorithm more responsive.": "", "Info": "ინფორმაცია", "Inject the entire content as context for comprehensive processing, this is recommended for complex queries.": "", "Input commands": "შეიყვანეთ ბრძანებები", "Input Variables": "", "Insert": "", "Insert Prompt as Rich Text": "", "Install from Github URL": "დაყენება Github-ის ბმულიდან", "Instant Auto-Send After Voice Transcription": "", "Integration": "", "Interface": "ინტერფეისი", "Invalid file content": "", "Invalid file format.": "არასწორი ფაილის ფორმატი.", "Invalid JSON file": "", "Invalid Tag": "არასწორი ჭდე", "is typing...": "", "Italic": "", "January": "იანვარი", "Jina API Key": "", "join our Discord for help.": "დახმარებისთვის შემოდით ჩვენს Discord-ზე.", "JSON": "JSON", "JSON Preview": "JSON გადახედვა", "July": "ივლისი", "June": "ივნისი", "Jupyter Auth": "", "Jupyter URL": "", "JWT Expiration": "JWT-ის ვადა", "JWT Token": "JWT ტოკენი", "Kagi Search API Key": "", "Keep in Sidebar": "", "Key": "გასაღები", "Keyboard shortcuts": "კლავიატურის მალსახმობები", "Knowledge": "ცოდნა", "Knowledge Access": "", "Knowledge Base": "", "Knowledge created successfully.": "", "Knowledge deleted successfully.": "", "Knowledge Public Sharing": "", "Knowledge reset successfully.": "", "Knowledge updated successfully": "", "Kokoro.js (Browser)": "Kokoro.js (ბრაუზერი)", "Kokoro.js Dtype": "Kokoro.js Dtype", "Label": "ჭდე", "Landing Page Mode": "", "Language": "ენა", "Language Locales": "", "Languages": "", "Last Active": "ბოლოს აქტიური", "Last Modified": "ბოლო ცვლილება", "Last reply": "", "LDAP": "LDAP", "LDAP server updated": "", "Leaderboard": "ლიდერების დაფა", "Learn more about OpenAPI tool servers.": "", "Leave empty for no compression": "", "Leave empty for unlimited": "", "Leave empty to include all models from \"{{url}}\" endpoint": "", "Leave empty to include all models from \"{{url}}/api/tags\" endpoint": "", "Leave empty to include all models from \"{{url}}/models\" endpoint": "", "Leave empty to include all models or select specific models": "", "Leave empty to use the default prompt, or enter a custom prompt": "", "Leave model field empty to use the default model.": "", "License": "ლიცენზია", "Light": "ღია", "Listening...": "", "Llama.cpp": "Llama.cpp", "LLMs can make mistakes. Verify important information.": "LLM-ებმა, შეიძლება, შეცდომები დაუშვან. გადაამოწმეთ მნიშვნელოვანი ინფორმაცია.", "Loader": "", "Loading Kokoro.js...": "", "Local": "ლოკალური", "Local Task Model": "", "Location access not allowed": "", "Lost": "წაგება", "LTR": "LTR", "Made by Open WebUI Community": "შექმნილია OpenWebUI საზოგადოების მიერ", "Make password visible in the user interface": "", "Make sure to enclose them with": "დარწმუნდით, რომ ჩასვით ისინი", "Make sure to export a workflow.json file as API format from ComfyUI.": "", "Manage": "მართვა", "Manage Direct Connections": "", "Manage Models": "", "Manage Ollama": "", "Manage Ollama API Connections": "", "Manage OpenAI API Connections": "", "Manage Pipelines": "მილსადენების მართვა", "Manage Tool Servers": "", "March": "მარტი", "Markdown": "", "Markdown (Header)": "", "Max Speakers": "", "Max Upload Count": "", "Max Upload Size": "", "Maximum of 3 models can be downloaded simultaneously. Please try again later.": "ერთდროულად მაქსიმუმ 3 მოდელის ჩამოტვირთვაა შესაძლებელია. მოგვიანებით სცადეთ.", "May": "მაისი", "Memories accessible by LLMs will be shown here.": "LLM-ებისთვის ხელმისაწვდომი მეხსიერებები აქ გამოჩნდება.", "Memory": "მეხსიერება", "Memory added successfully": "", "Memory cleared successfully": "", "Memory deleted successfully": "", "Memory updated successfully": "", "Merge Responses": "პასუხების შერწყმა", "Merged Response": "შერწყმული პასუხი", "Message rating should be enabled to use this feature": "", "Messages you send after creating your link won't be shared. Users with the URL will be able to view the shared chat.": "შეტყობინებები, რომელსაც თქვენ აგზავნით თქვენი ბმულის შექმნის შემდეგ, არ იქნება გაზიარებული. URL– ის მქონე მომხმარებლებს შეეძლებათ ნახონ საერთო ჩატი.", "Microsoft OneDrive": "", "Microsoft OneDrive (personal)": "", "Microsoft OneDrive (work/school)": "", "Mistral OCR": "", "Mistral OCR API Key required.": "", "Model": "მოდელი", "Model '{{modelName}}' has been successfully downloaded.": "მოდელის „{{modelName}}“ გადმოწერა წარმატებით დასრულდა.", "Model '{{modelTag}}' is already in queue for downloading.": "მოდელი „{{modelTag}}“ უკვე გადმოწერის რიგშია.", "Model {{modelId}} not found": "მოდელი {{modelId}} აღმოჩენილი არაა", "Model {{modelName}} is not vision capable": "Model {{modelName}} is not vision capable", "Model {{name}} is now {{status}}": "Model {{name}} is now {{status}}", "Model {{name}} is now hidden": "", "Model {{name}} is now visible": "", "Model accepts file inputs": "", "Model accepts image inputs": "", "Model can execute code and perform calculations": "", "Model can generate images based on text prompts": "", "Model can search the web for information": "", "Model created successfully!": "", "Model filesystem path detected. Model shortname is required for update, cannot continue.": "აღმოჩენილია მოდელის ფაილური სისტემის ბილიკი. განახლებისთვის საჭიროა მოდელის მოკლე სახელი, გაგრძელება შეუძლებელია.", "Model Filtering": "მოდელების გაფილტვრა", "Model ID": "მოდელის ID", "Model IDs": "მოდელის ID-ები", "Model Name": "Მოდელის სახელი", "Model not selected": "მოდელი არჩეული არაა", "Model Params": "მოდელის პარამეტრები", "Model Permissions": "მოდელის წვდომები", "Model unloaded successfully": "", "Model updated successfully": "", "Model(s) do not support file upload": "", "Modelfile Content": "მოდელის ფაილის შემცველობა", "Models": "მოდელები", "Models Access": "მოდელის წვდომა", "Models configuration saved successfully": "", "Models Public Sharing": "", "Mojeek Search API Key": "", "more": "მეტი", "More": "მეტი", "Name": "სახელი", "Name your knowledge base": "", "Native": "საკუთარი", "New Chat": "ახალი მიმოწერა", "New Folder": "ახალი საქაღალდე", "New Function": "", "New Note": "", "New Password": "ახალი პაროლი", "New Tool": "", "new-channel": "new-channel", "Next message": "", "No chats found for this user.": "", "No chats found.": "", "No content": "", "No content found": "შემცველობა აღმოჩენილი არაა", "No content found in file.": "", "No content to speak": "წარმოსათქმელი შემცველობის გარეშე", "No distance available": "მანძილი ხელმისაწვდომი არაა", "No feedbacks found": "", "No file selected": "ფაილი არჩეული არაა", "No groups with access, add a group to grant access": "", "No HTML, CSS, or JavaScript content found.": "", "No inference engine with management support found": "", "No knowledge found": "", "No memories to clear": "", "No model IDs": "მოდელის ID-ების გარეშე", "No models found": "მოდელები აღმოჩენილი არაა", "No models selected": "მოდელები არჩეული არაა", "No Notes": "", "No results found": "შედეგების გარეშე", "No search query generated": "ძებნის მოთხოვნა არ შექმნილა", "No source available": "წყარო ხელმისაწვდომი არაა", "No users were found.": "მომხმარებლები აღმოჩენილი არაა.", "No valves to update": "", "None": "არცერთი", "Not factually correct": "მთლად სწორი არაა", "Not helpful": "სასარგებლო არაა", "Note deleted successfully": "", "Note: If you set a minimum score, the search will only return documents with a score greater than or equal to the minimum score.": "შენიშვნა: თუ თქვენ დააყენებთ მინიმალურ ქულას, ძებნა დააბრუნებს მხოლოდ დოკუმენტებს მინიმალური ქულის მეტი ან ტოლი ქულით.", "Notes": "შენიშვნები", "Notification Sound": "გაფრთხილების ხმა", "Notification Webhook": "", "Notifications": "გაფრთხილებები", "November": "ნოემბერი", "OAuth ID": "OAuth ID", "October": "ოქტომბერი", "Off": "გამორთ", "Okay, Let's Go!": "აბა, წავედით!", "OLED Dark": "OLED მუქი", "Ollama": "Ollama", "Ollama API": "Ollama API", "Ollama API settings updated": "", "Ollama Version": "Ollama ვერსია", "On": "ჩართული", "OneDrive": "", "Only active when \"Paste Large Text as File\" setting is toggled on.": "", "Only active when the chat input is in focus and an LLM is generating a response.": "", "Only alphanumeric characters and hyphens are allowed": "", "Only alphanumeric characters and hyphens are allowed in the command string.": "ბრძანების სტრიქონში დაშვებულია, მხოლოდ, ალფარიცხვითი სიმბოლოები და ტირეები.", "Only collections can be edited, create a new knowledge base to edit/add documents.": "", "Only markdown files are allowed": "", "Only select users and groups with permission can access": "", "Oops! Looks like the URL is invalid. Please double-check and try again.": "უი! როგორც ჩანს, მისამართი არასწორია. გთხოვთ, გადაამოწმოთ და ისევ სცადოთ.", "Oops! There are files still uploading. Please wait for the upload to complete.": "", "Oops! There was an error in the previous response.": "", "Oops! You're using an unsupported method (frontend only). Please serve the WebUI from the backend.": "ვაი! იყენებთ მხარდაუჭერელ მეთოდს (მხოლოდ წინაბოლო). შედით WebUI-ზე უკანაბოლოდან.", "Open file": "ფაილის გახსნა", "Open in full screen": "მთელ ეკრანზე გახსნა", "Open modal to configure connection": "", "Open new chat": "ახალი ჩატის გახსნა", "Open WebUI can use tools provided by any OpenAPI server.": "", "Open WebUI uses faster-whisper internally.": "", "Open WebUI uses SpeechT5 and CMU Arctic speaker embeddings.": "", "Open WebUI version (v{{OPEN_WEBUI_VERSION}}) is lower than required version (v{{REQUIRED_VERSION}})": "", "OpenAI": "OpenAI", "OpenAI API": "OpenAI API", "OpenAI API Config": "OpenAI API პარამეტრები", "OpenAI API Key is required.": "OpenAI API გასაღები აუცილებელია.", "OpenAI API settings updated": "", "OpenAI URL/Key required.": "OpenAI URL/Key აუცილებელია.", "openapi.json URL or Path": "", "Options for running a local vision-language model in the picture description. The parameters refer to a model hosted on Hugging Face. This parameter is mutually exclusive with picture_description_api.": "", "or": "ან", "Ordered List": "", "Organize your users": "", "Other": "სხვა", "OUTPUT": "", "Output format": "გამოტანის ფორმატი", "Output Format": "", "Overview": "მიმოხილვა", "page": "პანელი", "Paginate": "", "Parameters": "", "Password": "პაროლი", "Paste Large Text as File": "", "PDF document (.pdf)": "PDF დოკუმენტი (.pdf)", "PDF Extract Images (OCR)": "PDF იდან ამოღებული სურათები (OCR)", "pending": "დარჩენილი", "Pending": "", "Pending User Overlay Content": "", "Pending User Overlay Title": "", "Permission denied when accessing media devices": "", "Permission denied when accessing microphone": "", "Permission denied when accessing microphone: {{error}}": "ნებართვა უარყოფილია მიკროფონზე წვდომისას: {{error}}", "Permissions": "ნებართვები", "Perplexity API Key": "", "Perplexity Model": "", "Perplexity Search Context Usage": "", "Personalization": "პერსონალიზაცია", "Picture Description API Config": "", "Picture Description Local Config": "", "Picture Description Mode": "", "Pin": "მიმაგრება", "Pinned": "მიმაგრებულია", "Pioneer insights": "", "Pipe": "", "Pipeline deleted successfully": "", "Pipeline downloaded successfully": "", "Pipelines": "მილსადენები", "Pipelines Not Detected": "", "Pipelines Valves": "მილსადენის სარქველები", "Plain text (.md)": "", "Plain text (.txt)": "უბრალო ტექსტი (.txt)", "Playground": "საცდელი ფუნქციები", "Playwright Timeout (ms)": "", "Playwright WebSocket URL": "", "Please carefully review the following warnings:": "", "Please do not close the settings page while loading the model.": "", "Please enter a prompt": "", "Please enter a valid path": "", "Please enter a valid URL": "", "Please fill in all fields.": "შეავსეთ ყველა ველი ბოლომდე.", "Please select a model first.": "ჯერ აირჩიეთ მოდელი, გეთაყვა.", "Please select a model.": "აირჩიეთ მოდელი.", "Please select a reason": "აირჩიეთ მიზეზი", "Port": "პორტი", "Positive attitude": "პოზიტიური დამოკიდებულება", "Prefix ID": "პრეფიქსის ", "Prefix ID is used to avoid conflicts with other connections by adding a prefix to the model IDs - leave empty to disable": "", "Prevent file creation": "", "Preview": "", "Previous 30 days": "წინა 30 დღე", "Previous 7 days": "წინა 7 დღე", "Previous message": "", "Private": "", "Profile Image": "პროფილის სურათი", "Prompt": "ბრძანების შეყვანის შეხსენება", "Prompt (e.g. Tell me a fun fact about the Roman Empire)": "Prompt (მაგ. მითხარი სახალისო ფაქტი რომის იმპერიის შესახებ)", "Prompt Autocompletion": "", "Prompt Content": "მოთხოვნის შემცველობა", "Prompt created successfully": "", "Prompt suggestions": "მოთხოვნის რჩევები", "Prompt updated successfully": "", "Prompts": "მოთხოვნები", "Prompts Access": "", "Prompts Public Sharing": "", "Public": "", "Pull \"{{searchValue}}\" from Ollama.com": "\"{{searchValue}}\"-ის გადმოწერა Ollama.com-იდან", "Pull a model from Ollama.com": "მოდელის გადმოწერა Ollama.com-დან", "Query Generation Prompt": "", "RAG Template": "RAG შაბლონი", "Rating": "ხმის მიცემა", "Re-rank models by topic similarity": "", "Read": "წაკითხვა", "Read Aloud": "ხმამაღლა წაკითხვა", "Reason": "", "Reasoning Effort": "", "Record": "", "Record voice": "ხმის ჩაწერა", "Redirecting you to Open WebUI Community": "მიმდინარეობს გადამისამართება OpenWebUI-ის საზოგადოების საიტზე", "Reduces the probability of generating nonsense. A higher value (e.g. 100) will give more diverse answers, while a lower value (e.g. 10) will be more conservative.": "", "Refer to yourself as \"User\" (e.g., \"User is learning Spanish\")": "", "References from": "", "Refused when it shouldn't have": "უარა, როგორც უნდა იყოს", "Regenerate": "თავიდან გენერაცია", "Reindex": "", "Reindex Knowledge Base Vectors": "", "Release Notes": "გამოცემის შენიშვნები", "Releases": "", "Relevance": "შესაბამისობა", "Relevance Threshold": "", "Remember Dismissal": "", "Remove": "წაშლა", "Remove {{MODELID}} from list.": "", "Remove file": "", "Remove File": "", "Remove image": "", "Remove Model": "მოდელის წაშლა", "Remove this tag from list": "", "Rename": "სახელის გადარქმევა", "Reorder Models": "", "Reply in Thread": "ნაკადში პასუხი", "Reranking Engine": "", "Reranking Model": "Reranking მოდელი", "Reset": "ჩამოყრა", "Reset All Models": "", "Reset Upload Directory": "", "Reset Vector Storage/Knowledge": "", "Reset view": "", "Response": "", "Response notifications cannot be activated as the website permissions have been denied. Please visit your browser settings to grant the necessary access.": "", "Response splitting": "", "Response Watermark": "", "Result": "შედეგი", "Retrieval": "", "Retrieval Query Generation": "", "Rich Text Input for Chat": "", "RK": "RK", "Role": "როლი", "Rosé Pine": "ვარდისფერი ფიჭვი", "Rosé Pine Dawn": "ვარდისფერი ფიჭვის გარიჟრაჟი", "RTL": "RTL", "Run": "გაშვება", "Running": "გაშვებულია", "Save": "შენახვა", "Save & Create": "შენახვა და შექმნა", "Save & Update": "შენახვა და განახლება", "Save As Copy": "ასლის შენახვა", "Save Tag": "ჭდის შენახვა", "Saved": "შენახულია", "Saving chat logs directly to your browser's storage is no longer supported. Please take a moment to download and delete your chat logs by clicking the button below. Don't worry, you can easily re-import your chat logs to the backend through": "ჩეთის ისტორიის შენახვა პირდაპირ თქვენი ბრაუზერის საცავში აღარ არის მხარდაჭერილი. გთხოვთ, დაუთმოთ და წაშალოთ თქვენი ჩატის ჟურნალები ქვემოთ მოცემულ ღილაკზე დაწკაპუნებით. არ ინერვიულოთ, თქვენ შეგიძლიათ მარტივად ხელახლა შემოიტანოთ თქვენი ჩეთის ისტორია ბექენდში", "Scroll On Branch Change": "", "Search": "ძებნა", "Search a model": "მოდელის ძებნა", "Search Base": "ბაზის ძებნა", "Search Chats": "ძებნა ჩატებში", "Search Collection": "კოლექციის ძებნა", "Search Filters": "ფილტრების ძებნა", "search for tags": "ჭდეების ძებნა", "Search Functions": "ფუნქციების ძებნა", "Search Knowledge": "", "Search Models": "მოდელების ძებნა", "Search Notes": "", "Search options": "ძებნის მორგება", "Search Prompts": "მოთხოვნების ძებნა", "Search Result Count": "ძიების შედეგების რაოდენობა", "Search the internet": "ინტერნეტში ძებნა", "Search Tools": "ძებნის ხელსაწყოები", "SearchApi API Key": "SearchApi API-ის გასაღები", "SearchApi Engine": "ძრავა SearchApi", "Searched {{count}} sites": "მოძებნილია {{count}} საიტზე", "Searching \"{{searchQuery}}\"": "მიმდინარეობს ძებნა \"{{searchQuery}}\"", "Searching Knowledge for \"{{searchQuery}}\"": "", "Searching the web...": "", "Searxng Query URL": "Searxng Query URL", "See readme.md for instructions": "ინსტრუქციებისთვის იხილეთ readme.md", "See what's new": "ნახეთ, რა არის ახალი", "Seed": "თესლი", "Select a base model": "აირჩიეთ საბაზისო მოდელი", "Select a engine": "აირჩიეთ ძრავა", "Select a function": "აირჩიეთ ფუნქცია", "Select a group": "აირჩიეთ ჯგუფი", "Select a model": "აირჩიეთ მოდელი", "Select a pipeline": "აირჩიეთ მილსადენი", "Select a pipeline url": "აირჩიეთ მილსადენის url", "Select a tool": "აირჩიეთ ხელსაწყო", "Select an auth method": "", "Select an Ollama instance": "", "Select Engine": "", "Select Knowledge": "", "Select only one model to call": "", "Selected model(s) do not support image inputs": "მონიშნულ მოდელებს გამოსახულების შეყვანის მხარდაჭერა არ გააჩნიათ", "Semantic distance to query": "", "Send": "გაგზავნა", "Send a Message": "შეტყობინების გაგზავნა", "Send message": "შეტყობინების გაგზავნა", "Sends `stream_options: { include_usage: true }` in the request.\nSupported providers will return token usage information in the response when set.": "", "September": "სექტემბერი", "SerpApi API Key": "", "SerpApi Engine": "", "Serper API Key": "Serper API-ის გასაღები", "Serply API Key": "", "Serpstack API Key": "Serpstack API-ის გასაღები", "Server connection verified": "სერვერთან კავშირი გადამოწმებულია", "Set as default": "ნაგულისხმევად დაყენება", "Set CFG Scale": "", "Set Default Model": "ნაგულისხმევი მოდელის დაყენება", "Set embedding model": "", "Set embedding model (e.g. {{model}})": "ჩვენება ჩაშენებული მოდელის დაყენება (მაგ. {{model}})", "Set Image Size": "გამოსახულების ზომის დაყენება", "Set reranking model (e.g. {{model}})": "Reranking მოდელის დაყენება (მაგ. {{model}})", "Set Sampler": "", "Set Scheduler": "", "Set Steps": "ნაბიჯების დაყენება", "Set the number of layers, which will be off-loaded to GPU. Increasing this value can significantly improve performance for models that are optimized for GPU acceleration but may also consume more power and GPU resources.": "", "Set the number of worker threads used for computation. This option controls how many threads are used to process incoming requests concurrently. Increasing this value can improve performance under high concurrency workloads but may also consume more CPU resources.": "", "Set Voice": "ხმის დაყენება", "Set whisper model": "", "Sets a flat bias against tokens that have appeared at least once. A higher value (e.g., 1.5) will penalize repetitions more strongly, while a lower value (e.g., 0.9) will be more lenient. At 0, it is disabled.": "", "Sets a scaling bias against tokens to penalize repetitions, based on how many times they have appeared. A higher value (e.g., 1.5) will penalize repetitions more strongly, while a lower value (e.g., 0.9) will be more lenient. At 0, it is disabled.": "", "Sets how far back for the model to look back to prevent repetition.": "", "Sets the random number seed to use for generation. Setting this to a specific number will make the model generate the same text for the same prompt.": "", "Sets the size of the context window used to generate the next token.": "", "Sets the stop sequences to use. When this pattern is encountered, the LLM will stop generating text and return. Multiple stop patterns may be set by specifying multiple separate stop parameters in a modelfile.": "", "Settings": "მორგება", "Settings saved successfully!": "პარამეტრები შენახვა წარმატებულია!", "Share": "გაზიარება", "Share Chat": "ჩატის გაზიარება", "Share to Open WebUI Community": "გაზიარება Open WebUI-ის საზოგადოებასთან", "Sharing Permissions": "", "Shortcuts with an asterisk (*) are situational and only active under specific conditions.": "", "Show": "ჩვენება", "Show \"What's New\" modal on login": "", "Show Admin Details in Account Pending Overlay": "", "Show All": "", "Show image preview": "", "Show Less": "", "Show Model": "", "Show shortcuts": "მალსახმობების ჩვენება", "Show your support!": "", "Showcased creativity": "გამოკვეთილი კრეატიულობა", "Sign in": "შესვლა", "Sign in to {{WEBUI_NAME}}": "", "Sign in to {{WEBUI_NAME}} with LDAP": "", "Sign Out": "გასვლა", "Sign up": "რეგისტრაცია", "Sign up to {{WEBUI_NAME}}": "", "Significantly improves accuracy by using an LLM to enhance tables, forms, inline math, and layout detection. Will increase latency. Defaults to True.": "", "Signing in to {{WEBUI_NAME}}": "", "sk-1234": "", "Skip Cache": "", "Skip the cache and re-run the inference. Defaults to False.": "", "Sougou Search API sID": "", "Sougou Search API SK": "", "Source": "წყარო", "Speech Playback Speed": "", "Speech recognition error: {{error}}": "საუბრის ამოცნობის შეცდომა: {{error}}", "Speech-to-Text": "", "Speech-to-Text Engine": "საუბრიდან-ტექსტამდე-ის ძრავი", "Stop": "გაჩერება", "Stop Generating": "", "Stop Sequence": "შეჩერების მიმდევრობა", "Stream Chat Response": "", "Strikethrough": "", "Strip Existing OCR": "", "Strip existing OCR text from the PDF and re-run OCR. Ignored if Force OCR is enabled. Defaults to False.": "", "STT Model": "", "STT Settings": "STT-ის მორგება", "Stylized PDF Export": "", "Subtitle (e.g. about the Roman Empire)": "სუბტიტრები (მაგ. რომის იმპერიის შესახებ)", "Success": "წარმატება", "Successfully updated.": "წარმატებით განახლდა.", "Suggested": "შეთავაზებულია", "Support": "მხარდაჭერა", "Support this plugin:": "", "Supported MIME Types": "", "Sync directory": "", "System": "სისტემა", "System Instructions": "", "System Prompt": "სისტემური მოთხოვნა", "Tags": "", "Tags Generation": "", "Tags Generation Prompt": "", "Tail free sampling is used to reduce the impact of less probable tokens from the output. A higher value (e.g., 2.0) will reduce the impact more, while a value of 1.0 disables this setting.": "", "Talk to model": "", "Tap to interrupt": "", "Task List": "", "Task Model": "", "Tasks": "ამოცანები", "Tavily API Key": "", "Tavily Extract Depth": "", "Tell us more:": "გვითხარით მეტი:", "Temperature": "ტემპერატურა", "Temporary Chat": "", "Text Splitter": "", "Text-to-Speech": "", "Text-to-Speech Engine": "ტექსტურ-ხმოვანი ძრავი", "Thanks for your feedback!": "მადლობა გამოხმაურებისთვის!", "The Application Account DN you bind with for search": "", "The base to search for users": "", "The batch size determines how many text requests are processed together at once. A higher batch size can increase the performance and speed of the model, but it also requires more memory.": "", "The developers behind this plugin are passionate volunteers from the community. If you find this plugin helpful, please consider contributing to its development.": "", "The evaluation leaderboard is based on the Elo rating system and is updated in real-time.": "", "The format to return a response in. Format can be json or a JSON schema.": "", "The height in pixels to compress images to. Leave empty for no compression.": "", "The language of the input audio. Supplying the input language in ISO-639-1 (e.g. en) format will improve accuracy and latency. Leave blank to automatically detect the language.": "", "The LDAP attribute that maps to the mail that users use to sign in.": "", "The LDAP attribute that maps to the username that users use to sign in.": "", "The leaderboard is currently in beta, and we may adjust the rating calculations as we refine the algorithm.": "", "The maximum file size in MB. If the file size exceeds this limit, the file will not be uploaded.": "", "The maximum number of files that can be used at once in chat. If the number of files exceeds this limit, the files will not be uploaded.": "", "The output format for the text. Can be 'json', 'markdown', or 'html'. Defaults to 'markdown'.": "", "The score should be a value between 0.0 (0%) and 1.0 (100%).": "რეიტინგი უნდა იყოს მნიშვნელობ შუალედიდან 0.0 (0%) - 1.0 (100%).", "The temperature of the model. Increasing the temperature will make the model answer more creatively.": "", "The width in pixels to compress images to. Leave empty for no compression.": "", "Theme": "თემა", "Thinking...": "", "This action cannot be undone. Do you wish to continue?": "", "This channel was created on {{createdAt}}. This is the very beginning of the {{channelName}} channel.": "", "This chat won't appear in history and your messages will not be saved.": "", "This ensures that your valuable conversations are securely saved to your backend database. Thank you!": "ეს უზრუნველყოფს, რომ თქვენი ღირებული საუბრები უსაფრთხოდ შეინახება თქვენს უკანაბოლო მონაცემთა ბაზაში. მადლობა!", "This feature is experimental and may be modified or discontinued without notice.": "", "This is an experimental feature, it may not function as expected and is subject to change at any time.": "", "This model is not publicly available. Please select another model.": "", "This option controls how long the model will stay loaded into memory following the request (default: 5m)": "", "This option controls how many tokens are preserved when refreshing the context. For example, if set to 2, the last 2 tokens of the conversation context will be retained. Preserving context can help maintain the continuity of a conversation, but it may reduce the ability to respond to new topics.": "", "This option enables or disables the use of the reasoning feature in Ollama, which allows the model to think before generating a response. When enabled, the model can take a moment to process the conversation context and generate a more thoughtful response.": "", "This option sets the maximum number of tokens the model can generate in its response. Increasing this limit allows the model to provide longer answers, but it may also increase the likelihood of unhelpful or irrelevant content being generated.": "", "This option will delete all existing files in the collection and replace them with newly uploaded files.": "", "This response was generated by \"{{model}}\"": "", "This will delete": "", "This will delete <strong>{{NAME}}</strong> and <strong>all its contents</strong>.": "", "This will delete all models including custom models": "", "This will delete all models including custom models and cannot be undone.": "", "This will reset the knowledge base and sync all files. Do you wish to continue?": "", "Thorough explanation": "საფუძვლიანი ახსნა", "Thought for {{DURATION}}": "", "Thought for {{DURATION}} seconds": "", "Tika": "", "Tika Server URL required.": "", "Tiktoken": "", "Tip: Update multiple variable slots consecutively by pressing the tab key in the chat input after each replacement.": "რჩევა: განაახლეთ რამდენიმე ცვლადი სლოტი თანმიმდევრულად, ყოველი ჩანაცვლების შემდეგ ჩატის ღილაკზე დაჭერით.", "Title": "სათაური", "Title (e.g. Tell me a fun fact)": "სათაური (მაგ. მითხარი რამე სასაცილო)", "Title Auto-Generation": "სათაურის ავტოგენერაცია", "Title cannot be an empty string.": "სათაურის ველი ცარიელი სტრიქონი ვერ იქნება.", "Title Generation": "", "Title Generation Prompt": "სათაურის შექმნის მოთხოვნა", "TLS": "TLS", "To access the available model names for downloading,": "ხელმისაწვდომი მოდელის სახელებთან წვდომისთვის, რომ გადმოწეროთ,", "To access the GGUF models available for downloading,": "გადმოსაწერად ხელმისაწვდომი GGUF მოდელებზე წვდომისთვის,", "To access the WebUI, please reach out to the administrator. Admins can manage user statuses from the Admin Panel.": "", "To attach knowledge base here, add them to the \"Knowledge\" workspace first.": "", "To learn more about available endpoints, visit our documentation.": "", "To protect your privacy, only ratings, model IDs, tags, and metadata are shared from your feedback—your chat logs remain private and are not included.": "", "To select actions here, add them to the \"Functions\" workspace first.": "", "To select filters here, add them to the \"Functions\" workspace first.": "", "To select toolkits here, add them to the \"Tools\" workspace first.": "", "Toast notifications for new updates": "", "Today": "დღეს", "Toggle search": "", "Toggle settings": "პარამეტრების გადართვა", "Toggle sidebar": "გვერდითი ზოლის ჩართ/გამორთ", "Toggle whether current connection is active.": "", "Token": "კოდი", "Too verbose": "", "Tool created successfully": "", "Tool deleted successfully": "", "Tool Description": "", "Tool ID": "", "Tool imported successfully": "", "Tool Name": "ხელსაწყოს სახელი", "Tool Servers": "", "Tool updated successfully": "", "Tools": "ხელსაწყოები", "Tools Access": "", "Tools are a function calling system with arbitrary code execution": "", "Tools Function Calling Prompt": "", "Tools have a function calling system that allows arbitrary code execution.": "", "Tools Public Sharing": "", "Top K": "ტოპ K", "Top K Reranker": "", "Transformers": "", "Trouble accessing Ollama?": "Ollama-ს ვერ უკავშირდები?", "Trust Proxy Environment": "", "TTS Model": "", "TTS Settings": "TTS პარამეტრები", "TTS Voice": "", "Type": "ტიპი", "Type Hugging Face Resolve (Download) URL": "აკრიფეთ HuggingFace-ის ამოხსნის (გადმოწერის) URL", "Uh-oh! There was an issue with the response.": "", "UI": "UI", "Unarchive All": "", "Unarchive All Archived Chats": "", "Unarchive Chat": "", "Underline": "", "Unloads {{FROM_NOW}}": "", "Unlock mysteries": "", "Unpin": "ჩამოხსნა", "Unravel secrets": "", "Untagged": "ჭდის გარეშე", "Untitled": "", "Update": "განახლება", "Update and Copy Link": "განახლება და ბმულის კოპირება", "Update for the latest features and improvements.": "", "Update password": "პაროლის განახლება", "Updated": "განახლებულია", "Updated at": "განახლების დრო", "Updated At": "", "Upgrade to a licensed plan for enhanced capabilities, including custom theming and branding, and dedicated support.": "", "Upload": "ატვირთვა", "Upload a GGUF model": "GGUF მოდელის ატვირთვა", "Upload Audio": "", "Upload directory": "ატვირთვის დირექტორია", "Upload files": "ფაილების ატვირთვა", "Upload Files": "ფაილების ატვირთვა", "Upload Pipeline": "", "Upload Progress": "ატვირთვის მიმდინარეობა", "URL": "URL", "URL Mode": "URL რეჟიმი", "Usage": "", "Use '#' in the prompt input to load and include your knowledge.": "", "Use Gravatar": "Gravatar-ის გამოყენება", "Use groups to group your users and assign permissions.": "", "Use Initials": "ინიციალების გამოყენება", "Use LLM": "", "Use no proxy to fetch page contents.": "", "Use proxy designated by http_proxy and https_proxy environment variables to fetch page contents.": "", "user": "მომხმარებელი", "User": "მომხმარებელი", "User location successfully retrieved.": "", "User menu": "", "User Webhooks": "", "Username": "მომხმარებლის სახელი", "Users": "მომხმარებლები", "Using the default arena model with all models. Click the plus button to add custom models.": "", "Utilize": "გამოყენება", "Valid time units:": "სწორი დროის ერთეულები:", "Valves": "", "Valves updated": "", "Valves updated successfully": "", "variable": "ცვლადი", "variable to have them replaced with clipboard content.": "ცვლადი მისი ბუფერის მნიშვნელობით ჩასანაცვლებლად.", "Verify Connection": "", "Verify SSL Certificate": "", "Version": "ვერსია", "Version {{selectedVersion}} of {{totalVersions}}": "", "View Replies": "", "View Result from **{{NAME}}**": "", "Visibility": "ხილვადობა", "Vision": "", "Voice": "ხმა", "Voice Input": "", "Voice mode": "", "Warning": "გაფრთხილება", "Warning:": "გაფრთხილება:", "Warning: Enabling this will allow users to upload arbitrary code on the server.": "", "Warning: If you update or change your embedding model, you will need to re-import all documents.": "გაფრთხილება: თუ განაახლებთ ან შეცვლით თქვენს ჩაშენებულ მოდელს, მოგიწევთ ყველა დოკუმენტის ხელახლა შემოტანა.", "Warning: Jupyter execution enables arbitrary code execution, posing severe security risks—proceed with extreme caution.": "", "Web": "ვები", "Web API": "", "Web Loader Engine": "", "Web Search": "ვებში ძებნა", "Web Search Engine": "ვებ საძიებო სისტემა", "Web Search in Chat": "", "Web Search Query Generation": "", "Webhook URL": "Webhook URL", "WebUI Settings": "WebUI პარამეტრები", "WebUI URL": "", "WebUI will make requests to \"{{url}}\"": "", "WebUI will make requests to \"{{url}}/api/chat\"": "", "WebUI will make requests to \"{{url}}/chat/completions\"": "", "Weight of BM25 Retrieval": "", "What are you trying to achieve?": "", "What are you working on?": "", "What's New in": "რა არის ახალი", "When enabled, the model will respond to each chat message in real-time, generating a response as soon as the user sends a message. This mode is useful for live chat applications, but may impact performance on slower hardware.": "", "wherever you are": "", "Whether to paginate the output. Each page will be separated by a horizontal rule and page number. Defaults to False.": "", "Whisper (Local)": "", "Why?": "რატომ?", "Widescreen Mode": "", "Won": "ვონი", "Works together with top-k. A higher value (e.g., 0.95) will lead to more diverse text, while a lower value (e.g., 0.5) will generate more focused and conservative text.": "", "Workspace": "სამუშაო სივრცე", "Workspace Permissions": "", "Write": "ჩაწერა", "Write a prompt suggestion (e.g. Who are you?)": "დაწერეთ მოკლე წინადადება (მაგ. ვინ ხარ?", "Write a summary in 50 words that summarizes [topic or keyword].": "დაწერეთ რეზიუმე 50 სიტყვით, რომელიც აჯამებს [თემას ან საკვანძო სიტყვას].", "Write something...": "", "Yacy Instance URL": "", "Yacy Password": "", "Yacy Username": "", "Yesterday": "გუშინ", "You": "თქვენ", "You are currently using a trial license. Please contact support to upgrade your license.": "", "You can only chat with a maximum of {{maxCount}} file(s) at a time.": "", "You can personalize your interactions with LLMs by adding memories through the 'Manage' button below, making them more helpful and tailored to you.": "", "You cannot upload an empty file.": "", "You do not have permission to upload files.": "", "You have no archived conversations.": "დაარქივებული საუბრები არ გაქვთ.", "You have shared this chat": "თქვენ გააზიარეთ ეს ჩატი", "You're a helpful assistant.": "თქვენ სასარგებლო ასისტენტი ბრძანდებით.", "You're now logged in.": "ახლა შესული ბრძანდებით.", "Your account status is currently pending activation.": "", "Your entire contribution will go directly to the plugin developer; Open WebUI does not take any percentage. However, the chosen funding platform might have its own fees.": "", "Youtube": "Youtube", "Youtube Language": "", "Youtube Proxy URL": ""}