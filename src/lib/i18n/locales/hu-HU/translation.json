{"-1 for no limit, or a positive integer for a specific limit": "-1 a korlátlanhoz, vagy pozitív egész szám egy konkrét limithoz", "'s', 'm', 'h', 'd', 'w' or '-1' for no expiration.": "'s', 'm', 'h', 'd', 'w' vagy '-1' ha nincs lej<PERSON>rat.", "(e.g. `sh webui.sh --api --api-auth username_password`)": "(pl. `sh webui.sh --api --api-auth username_password`)", "(e.g. `sh webui.sh --api`)": "(pl. `sh webui.sh --api`)", "(latest)": "(<PERSON><PERSON><PERSON><PERSON>)", "(leave blank for to use commercial endpoint)": "", "[Last] dddd [at] h:mm A": "", "[Today at] h:mm A": "", "[Yesterday at] h:mm A": "", "{{ models }}": "{{ modelle<PERSON> }}", "{{COUNT}} Available Tools": "{{COUNT}} Elérhető eszköz", "{{COUNT}} characters": "", "{{COUNT}} hidden lines": "{{COUNT}} rejtett sor", "{{COUNT}} Replies": "{{COUNT}} V<PERSON><PERSON>z", "{{COUNT}} words": "", "{{user}}'s Chats": "{{user}} <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "{{webUIName}} Backend Required": "{{webUIName}} Backend szükséges", "*Prompt node ID(s) are required for image generation": "*Prompt node ID(k) szükségesek a képgeneráláshoz", "A new version (v{{LATEST_VERSION}}) is now available.": "<PERSON><PERSON> (v{{LATEST_VERSION}}) érhető el.", "A task model is used when performing tasks such as generating titles for chats and web search queries": "A feladat modell olyan fela<PERSON><PERSON><PERSON><PERSON>, mint a beszélgetések címeinek generálása és webes keresési lekérdezések", "a user": "e<PERSON>", "About": "Névjegy", "Accept autocomplete generation / Jump to prompt variable": "Automatikus kiegészítés elfogadása / Ugrás a prompt változóhoz", "Access": "Hozzáf<PERSON><PERSON><PERSON>", "Access Control": "Hozzáférés-vezérlés", "Accessible to all users": "Minden felhasználó sz<PERSON>mára elérhető", "Account": "<PERSON>ók", "Account Activation Pending": "Fiók aktiválása folyamatban", "Accurate information": "Pontos információ", "Action": "", "Actions": "Műveletek", "Activate": "Aktiválás", "Activate this command by typing \"/{{COMMAND}}\" to chat input.": "Aktiváld ezt a parancsot a \"/{{COMMAND}}\" beírásával a csevegőmezőbe.", "Active Users": "Aktív felhasználók", "Add": "Hozzáadás", "Add a model ID": "<PERSON><PERSON> a<PERSON><PERSON><PERSON><PERSON><PERSON> ho<PERSON>", "Add a short description about what this model does": "<PERSON>j hozzá egy r<PERSON><PERSON>, hogy mit csinál ez a modell", "Add a tag": "<PERSON><PERSON><PERSON><PERSON>", "Add Arena Model": "Arena modell hozzáadása", "Add Connection": "<PERSON><PERSON><PERSON><PERSON><PERSON> ho<PERSON>", "Add Content": "Tartalom <PERSON>", "Add content here": "Tartalom hozzáadása ide", "Add Custom Parameter": "", "Add custom prompt": "<PERSON><PERSON><PERSON><PERSON> prompt ho<PERSON><PERSON><PERSON><PERSON><PERSON>", "Add Files": "Fájlok hozzáadása", "Add Group": "Csoport hozzáadása", "Add Memory": "Memória hozzáadása", "Add Model": "<PERSON><PERSON>", "Add Reaction": "<PERSON><PERSON><PERSON><PERSON>", "Add Tag": "<PERSON><PERSON><PERSON><PERSON>", "Add Tags": "Címkék hozzáadása", "Add text content": "Szöveges tartalom hozzáadása", "Add User": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Add User Group": "Felhasználói csoport hozzáadása", "Adjusting these settings will apply changes universally to all users.": "Ezen beállítások módosítása minden felhasználóra érvényes lesz.", "admin": "admin", "Admin": "Admin", "Admin Panel": "Admin Panel", "Admin Settings": "<PERSON><PERSON>", "Admins have access to all tools at all times; users need tools assigned per model in the workspace.": "Az adminok mindig hozzáférnek minden eszközhöz; a felhasználóknak modellenként kell eszközöket hozzárendelni a munkaterületen.", "Advanced Parameters": "<PERSON><PERSON><PERSON>", "Advanced Params": "<PERSON><PERSON><PERSON>", "AI": "", "All": "Mind", "All Documents": "Minden dokumentum", "All models deleted successfully": "Minden modell sikeresen tö<PERSON>", "Allow Call": "", "Allow Chat Controls": "Csevegésvezérlők engedélyezése", "Allow Chat Delete": "Csevegés törlésének engedélyezése", "Allow Chat Deletion": "Beszélgetések törlésének engedélyezése", "Allow Chat Edit": "Csevegés szerkesztésének engedélyezése", "Allow Chat Export": "", "Allow Chat Share": "", "Allow Chat System Prompt": "", "Allow File Upload": "Fájlfeltöltés engedélyezése", "Allow Multiple Models in Chat": "", "Allow non-local voices": "<PERSON><PERSON> hely<PERSON> engedélyez<PERSON>e", "Allow Speech to Text": "", "Allow Temporary Chat": "Ideig<PERSON>s be<PERSON> engedélyezése", "Allow Text to Speech": "", "Allow User Location": "Felhasználói helyzet engedélyezése", "Allow Voice Interruption in Call": "Hang megszakítás engedélyezése hívás közben", "Allowed Endpoints": "Engedélyezett végpontok", "Allowed File Extensions": "", "Allowed file extensions for upload. Separate multiple extensions with commas. Leave empty for all file types.": "", "Already have an account?": "<PERSON><PERSON><PERSON>?", "Alternative to the top_p, and aims to ensure a balance of quality and variety. The parameter p represents the minimum probability for a token to be considered, relative to the probability of the most likely token. For example, with p=0.05 and the most likely token having a probability of 0.9, logits with a value less than 0.045 are filtered out.": "Alternatíva a top_p helyett, cé<PERSON><PERSON> a minőség és változatosság egyensúlyának biztosítása. A p paraméter a token figyelembevételének minimális valószínűségét jelzi a legvalószínűbb token valószínűségéhez képest. Például, ha p=0,05 és a legvalószínűbb token valószínűsége 0,9, a 0,045-nél kisebb értékű logitok kiszűrésre kerülnek.", "Always": "<PERSON><PERSON>", "Always Collapse Code Blocks": "Kódblokkok mindig összecsukása", "Always Expand Details": "Részletek mindig kibontása", "Always Play Notification Sound": "", "Amazing": "Csodálatos", "an assistant": "egy <PERSON>", "Analyzed": "Elemezve", "Analyzing...": "Elemzés...", "and": "és", "and {{COUNT}} more": "és még {{COUNT}} db", "and create a new shared link.": "és hozz létre egy új megosztott linket.", "Android": "", "API": "", "API Base URL": "API alap URL", "API details for using a vision-language model in the picture description. This parameter is mutually exclusive with picture_description_local.": "", "API Key": "API kulcs", "API Key created.": "API kulcs létrehozva.", "API Key Endpoint Restrictions": "API kulcs végpont korlátozások", "API keys": "API kulcsok", "API Version": "", "Application DN": "Alkalmazás DN", "Application DN Password": "Alkalmazás DN jelszó", "applies to all users with the \"user\" role": "minden \"felhasz<PERSON><PERSON><PERSON>\" szerepkörű felhasz<PERSON><PERSON><PERSON><PERSON>", "April": "<PERSON><PERSON><PERSON><PERSON>", "Archive": "Archiválás", "Archive All Chats": "Minden beszélgetés archiválása", "Archived Chats": "<PERSON><PERSON><PERSON><PERSON>", "archived-chat-export": "archivált csevegés exportálása", "Are you sure you want to clear all memories? This action cannot be undone.": "Biztosan törölni szeretnéd az összes memóriát? Ez a művelet nem von<PERSON>ó v<PERSON>za.", "Are you sure you want to delete this channel?": "Biztosan törölni szeretnéd ezt a csatornát?", "Are you sure you want to delete this message?": "Biztosan törölni szeretnéd ezt az üzenetet?", "Are you sure you want to unarchive all archived chats?": "Biztosan v<PERSON>za szeretnéd állítani az összes archivált csevegést?", "Are you sure?": "Biztos vagy benne?", "Arena Models": "Arena modellek", "Artifacts": "Műtermékek", "Ask": "Kérdezz", "Ask a question": "Kérdezz valamit", "Assistant": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Attach file from knowledge": "Fájl csatolása a tudásbázisból", "Attention to detail": "Részletekre való odafigyelés", "Attribute for Mail": "<PERSON><PERSON>", "Attribute for Username": "Felhasználónév attribútum", "Audio": "Hang", "August": "<PERSON><PERSON><PERSON><PERSON>", "Auth": "Hitelesítés", "Authenticate": "Hitelesítés", "Authentication": "Hitelesítés", "Auto": "Automatikus", "Auto-Copy Response to Clipboard": "Válasz automatikus másolása a vágólapra", "Auto-playback response": "Automatikus válasz le<PERSON>zás", "Autocomplete Generation": "Automatikus kiegészítés generálása", "Autocomplete Generation Input Max Length": "Automatikus kiegészítés bemenet maximá<PERSON> ho<PERSON>za", "Automatic1111": "Automatic1111", "AUTOMATIC1111 Api Auth String": "AUTOMATIC1111 Api hitelesí<PERSON><PERSON>", "AUTOMATIC1111 Base URL": "AUTOMATIC1111 alap URL", "AUTOMATIC1111 Base URL is required.": "AUTOMATIC1111 alap URL szükséges.", "Available list": "Elérhető lista", "Available Tools": "Elérhető eszközök", "available!": "elérhető!", "Awful": "Szörnyű", "Azure AI Speech": "Azure <PERSON> beszéd", "Azure Region": "Azure régió", "Back": "<PERSON><PERSON><PERSON>", "Bad Response": "Rossz válasz", "Banners": "<PERSON><PERSON>", "Base Model (From)": "Alap modell (Forrás)", "Base Model List Cache speeds up access by fetching base models only at startup or on settings save—faster, but may not show recent base model changes.": "", "before": "<PERSON><PERSON><PERSON>", "Being lazy": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Beta": "<PERSON><PERSON><PERSON>", "Bing Search V7 Endpoint": "Bing Search V7 végpont", "Bing Search V7 Subscription Key": "Bing Search V7 előfizetési kulcs", "Bocha Search API Key": "Bocha Search API kulcs", "Bold": "", "Boosting or penalizing specific tokens for constrained responses. Bias values will be clamped between -100 and 100 (inclusive). (Default: none)": "Specifikus tokenek növelése vagy büntetése korlátozott válaszokhoz. Az elfogultság értékei -100 és 100 között lesznek rögzítve (beleértve). (Alapértelmezett: nincs)", "Both Docling OCR Engine and Language(s) must be provided or both left empty.": "", "Brave Search API Key": "Brave Search API kulcs", "Bullet List": "", "By {{name}}": "Készítette: {{name}}", "Bypass Embedding and Retrieval": "Beágyazás és visszakeresés kihagyása", "Bypass Web Loader": "", "Cache Base Model List": "", "Calendar": "<PERSON><PERSON><PERSON><PERSON>", "Call": "Hívás", "Call feature is not supported when using Web STT engine": "A hívás funkció nem támogatott Web STT motor használatakor", "Camera": "<PERSON><PERSON><PERSON>", "Cancel": "<PERSON><PERSON><PERSON><PERSON>", "Capabilities": "Képességek", "Capture": "Rög<PERSON><PERSON><PERSON><PERSON>", "Capture Audio": "", "Certificate Path": "Tanúsítvány útvonal", "Change Password": "Jelszó módosítása", "Channel Name": "Csatorn<PERSON> neve", "Channels": "Csatornák", "Character": "<PERSON><PERSON><PERSON>", "Character limit for autocomplete generation input": "Karakterlimit az automatikus kiegészítés bemenetéhez", "Chart new frontiers": "<PERSON><PERSON>é<PERSON>z<PERSON>", "Chat": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Chat Background Image": "Beszélge<PERSON>s h<PERSON>ttérkép", "Chat Bubble UI": "Beszélgetés buborék felület", "Chat Controls": "Beszélgetés vezérlők", "Chat direction": "Beszélgetés iránya", "Chat Overview": "Beszélge<PERSON>s <PERSON>", "Chat Permissions": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Chat Tags Auto-Generation": "Beszélgetés címkék automatikus generálása", "Chats": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Check Again": "<PERSON><PERSON><PERSON><PERSON>", "Check for updates": "Frissítések keresése", "Checking for updates...": "Frissítések keresése...", "Choose a model before saving...": "Válassz modellt mentés előtt...", "Chunk Overlap": "<PERSON><PERSON>", "Chunk Size": "<PERSON><PERSON>", "Ciphers": "Titkosítási algoritmusok", "Citation": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Citations": "", "Clear memory": "Memória törlése", "Clear Memory": "Memória törlése", "click here": "kattints ide", "Click here for filter guides.": "Kattints ide a szűrő útmutatókért.", "Click here for help.": "Kattints ide segítségért.", "Click here to": "Kattints ide", "Click here to download user import template file.": "Kattints ide a felhasználó importálási sablon letöltéséhez.", "Click here to learn more about faster-whisper and see the available models.": "<PERSON><PERSON><PERSON> ide, hogy többet tudj meg a faster-whisperr<PERSON>l és lásd az elérhető modelleket.", "Click here to see available models.": "Kattints ide az elérhető modellek megtekintéséhez.", "Click here to select": "Kattints ide a kiválasztáshoz", "Click here to select a csv file.": "Kattints ide egy CSV fájl kiválasztásához.", "Click here to select a py file.": "Kattints ide egy py fájl kiválasztásához.", "Click here to upload a workflow.json file.": "Kattints ide egy workflow.json fájl feltöltéséhez.", "click here.": "kattints ide.", "Click on the user role button to change a user's role.": "Kattints a felhasználói szerep gombra a felhasználó szerepének módosításához.", "Clipboard write permission denied. Please check your browser settings to grant the necessary access.": "Vágólap írási engedély megtagadva. <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, el<PERSON><PERSON>rizd a böngésző beállításait a szükséges hozzáférés megadásához.", "Clone": "Klónozás", "Clone Chat": "Beszélgetés klónozása", "Clone of {{TITLE}}": "{{TITLE}} klónja", "Close": "Bezárás", "Close Configure Connection Modal": "", "Close modal": "", "Close settings modal": "", "Code Block": "", "Code execution": "Kód végrehajtás", "Code Execution": "Kód végrehajtás", "Code Execution Engine": "Kód végrehajtási motor", "Code Execution Timeout": "Kód végrehajtási időtúllépés", "Code formatted successfully": "<PERSON><PERSON><PERSON>", "Code Interpreter": "<PERSON><PERSON><PERSON>", "Code Interpreter Engine": "<PERSON><PERSON><PERSON> motor", "Code Interpreter Prompt Template": "<PERSON><PERSON><PERSON> prompt sablon", "Collapse": "Összecsukás", "Collection": "Gyűjtemény", "Color": "Szín", "ComfyUI": "ComfyUI", "ComfyUI API Key": "ComfyUI API kulcs", "ComfyUI Base URL": "ComfyUI alap URL", "ComfyUI Base URL is required.": "ComfyUI alap URL szükséges.", "ComfyUI Workflow": "ComfyUI munkafolyamat", "ComfyUI Workflow Nodes": "ComfyUI munkafolyamat csomópontok", "Command": "<PERSON><PERSON><PERSON>", "Comment": "", "Completions": "Kiegészítések", "Concurrent Requests": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Configure": "Konfigurálás", "Confirm": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Confirm Password": "<PERSON><PERSON><PERSON>ó megerősítése", "Confirm your action": "Erősítsd meg a műveletet", "Confirm your new password": "Erősítsd meg az új j<PERSON>zavad", "Connect to your own OpenAI compatible API endpoints.": "Csatlakozz saját OpenAI kompatibilis API végpontjaidhoz.", "Connect to your own OpenAPI compatible external tool servers.": "Csatlakozz saját OpenAPI kompatibilis külső eszköszervereidhez.", "Connection failed": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Connection successful": "<PERSON><PERSON><PERSON><PERSON><PERSON> si<PERSON>", "Connection Type": "", "Connections": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Connections saved successfully": "Kapcsolatok sikeresen mentve", "Connections settings updated": "", "Constrains effort on reasoning for reasoning models. Only applicable to reasoning models from specific providers that support reasoning effort.": "Korlátozza az érvelési erőfeszítést az érvelési modellek esetében. Csak bizonyos szolgáltatóktól származó, érvelési erőfeszítést támogató modellekre alkalmazható.", "Contact Admin for WebUI Access": "Lépj kapcsolatba az adminnal a WebUI hozzáférésért", "Content": "Tartalom", "Content Extraction Engine": "Tartalom kinyerési motor", "Continue Response": "Válasz folytatása", "Continue with {{provider}}": "Folytatás {{provider}} szolgáltatóval", "Continue with Email": "Folytatás <PERSON>l", "Continue with LDAP": "Folytatás LDAP-val", "Control how message text is split for TTS requests. 'Punctuation' splits into sentences, 'paragraphs' splits into paragraphs, and 'none' keeps the message as a single string.": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, hogyan legyen felosztva az üzenet szövege a TTS kérésekhez. A 'Központozás' monda<PERSON><PERSON> bontja, a 'Bekezdések' bekezdésekre bontja, a 'Nincs' pedig egyetlen szövegként kezeli az üzenetet.", "Control the repetition of token sequences in the generated text. A higher value (e.g., 1.5) will penalize repetitions more strongly, while a lower value (e.g., 1.1) will be more lenient. At 1, it is disabled.": "Szabályozza a generált szövegben lévő token sorozatok ismétlődését. <PERSON><PERSON><PERSON><PERSON> (pl. 1,5) er<PERSON><PERSON>bben bünteti az ismétléseket, alacsonyabb érték (pl. 1,1) engedékenyebb. 1-nél kikapcsolva.", "Controls": "Vezérlők", "Controls the balance between coherence and diversity of the output. A lower value will result in more focused and coherent text.": "Szabályozza a kimenet koherenciája és változatossága közötti egyensúlyt. Alacsonyabb érték fókuszáltabb és koherensebb szöveget eredményez.", "Copied": "M<PERSON>ol<PERSON>", "Copied link to clipboard": "", "Copied shared chat URL to clipboard!": "Megosztott beszélgetés URL másolva a vágólapra!", "Copied to clipboard": "Vágólapra másolva", "Copy": "Másolás", "Copy Formatted Text": "", "Copy last code block": "Utolsó kódblokk másolása", "Copy last response": "Utolsó válasz másolása", "Copy link": "", "Copy Link": "<PERSON>", "Copy to clipboard": "Másolás a vágólapra", "Copying to clipboard was successful!": "Sikeres másolás a vágólapra!", "CORS must be properly configured by the provider to allow requests from Open WebUI.": "A CORS-t megfelelően kell konfigurálnia a szolgáltatónak, hogy engedélyezze az Open WebUI-ból érkező kéréseket.", "Create": "Létrehozás", "Create a knowledge base": "Tudásb<PERSON><PERSON>s <PERSON>", "Create a model": "<PERSON><PERSON>", "Create Account": "Fiók létrehozása", "Create Admin Account": "<PERSON><PERSON>", "Create Channel": "Csatorna létrehozása", "Create Group": "Csoport létrehozása", "Create Knowledge": "Tudás l<PERSON>hoz<PERSON>", "Create new key": "Új kulcs létrehozása", "Create new secret key": "Új titkos kulcs létrehozása", "Create Note": "", "Create your first note by clicking on the plus button below.": "", "Created at": "Létrehozva", "Created At": "Létrehozva", "Created by": "Létrehozta", "CSV Import": "CSV importálás", "Ctrl+Enter to Send": "Ctrl+Enter a küldéshez", "Current Model": "Jelenlegi modell", "Current Password": "<PERSON><PERSON><PERSON><PERSON> j<PERSON>", "Custom": "<PERSON><PERSON><PERSON><PERSON>", "Custom description enabled": "", "Custom Parameter Name": "", "Custom Parameter Value": "", "Danger Zone": "Veszélyzóna", "Dark": "<PERSON><PERSON><PERSON><PERSON>", "Database": "Adatb<PERSON><PERSON><PERSON>", "Datalab Marker API": "", "Datalab Marker API Key required.": "", "DD/MM/YYYY": "", "December": "December", "Default": "Alap<PERSON><PERSON><PERSON><PERSON><PERSON>", "Default (Open AI)": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (Open AI)", "Default (SentenceTransformers)": "Alap<PERSON><PERSON><PERSON><PERSON>ett (SentenceTransformers)", "Default description enabled": "", "Default mode works with a wider range of models by calling tools once before execution. Native mode leverages the model's built-in tool-calling capabilities, but requires the model to inherently support this feature.": "Az alapértelmezett mód szélesebb modellválasztékkal működik az eszközök egyszeri meghívásával a végrehajtás előtt. A natív mód a modell beépített eszközhívási képességeit használja ki, de ehhez a modellnek eredendően támogatnia kell ezt a funkciót.", "Default Model": "Alapértelmezett modell", "Default model updated": "Alapértelmezett modell frissítve", "Default Models": "Alapértelmezett modellek", "Default permissions": "Alap<PERSON><PERSON><PERSON><PERSON><PERSON> en<PERSON><PERSON><PERSON><PERSON>", "Default permissions updated successfully": "Alapértelmezett enged<PERSON><PERSON><PERSON> si<PERSON>esen frissítve", "Default Prompt Suggestions": "Alap<PERSON><PERSON><PERSON><PERSON>ett prompt java<PERSON><PERSON>", "Default to 389 or 636 if TLS is enabled": "Alapértelmezés szerint 389 vagy 636, ha a TLS engedélyezve van", "Default to ALL": "Alapértelmezés szerint MIND", "Default to segmented retrieval for focused and relevant content extraction, this is recommended for most cases.": "Alapértelmezés szerint szegmentált visszakeresés a fókuszált és releváns tartalom kinyeréséhez, ez a legtöbb esetben ajánlott.", "Default User Role": "Alapértelmezett felhasználói szerep", "Delete": "Törlés", "Delete a model": "<PERSON><PERSON> törl<PERSON>", "Delete All Chats": "Minden beszélgetés törlése", "Delete All Models": "Minden modell törlése", "Delete chat": "Beszélgetés törlése", "Delete Chat": "Beszélgetés törlése", "Delete chat?": "Törli a beszélgetést?", "Delete folder?": "T<PERSON><PERSON><PERSON> a mapp<PERSON>t?", "Delete function?": "Törl<PERSON> a funkciót?", "Delete Message": "Üzenet törlése", "Delete message?": "Üzenet törlése?", "Delete note?": "", "Delete prompt?": "T<PERSON><PERSON><PERSON> a promptot?", "delete this link": "link törlése", "Delete tool?": "Törli az eszközt?", "Delete User": "Felhasz<PERSON><PERSON><PERSON> törl<PERSON>", "Deleted {{deleteModelTag}}": "{{deleteModelTag}} törölve", "Deleted {{name}}": "{{name}} tö<PERSON><PERSON><PERSON>", "Deleted User": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> t<PERSON>", "Deployment names are required for Azure OpenAI": "", "Describe Pictures in Documents": "", "Describe your knowledge base and objectives": "Írd le a tudásbázisodat és céljaidat", "Description": "Le<PERSON><PERSON><PERSON>", "Detect Artifacts Automatically": "", "Dictate": "", "Didn't fully follow instructions": "<PERSON>em k<PERSON> teljesen az utasításokat", "Direct": "Közvetlen", "Direct Connections": "Közvetlen ka<PERSON>tok", "Direct Connections allow users to connect to their own OpenAI compatible API endpoints.": "A közvetlen kapcsolatok lehetővé teszik a felhasználók számára, hogy saját OpenAI kompatibilis API végpontjaikhoz csatlakozzanak.", "Direct Tool Servers": "Közvetlen eszköszerverek", "Disable Code Interpreter": "", "Disable Image Extraction": "", "Disable image extraction from the PDF. If Use LLM is enabled, images will be automatically captioned. Defaults to False.": "", "Disabled": "<PERSON><PERSON><PERSON>", "Discover a function": "<PERSON><PERSON><PERSON> f<PERSON>", "Discover a model": "<PERSON><PERSON>", "Discover a prompt": "Prompt felfedezése", "Discover a tool": "Eszköz felfedezése", "Discover how to use Open WebUI and seek support from the community.": "<PERSON><PERSON><PERSON> fel, ho<PERSON>an használd az Open WebUI-t és kérj támogatást a közösségtől.", "Discover wonders": "Csodák felfedezése", "Discover, download, and explore custom functions": "<PERSON><PERSON><PERSON> fel, tölts le és fedezz fel egyéni funkciókat", "Discover, download, and explore custom prompts": "<PERSON><PERSON><PERSON> fel, tölts le és fedezz fel egyéni promptokat", "Discover, download, and explore custom tools": "<PERSON><PERSON>z fel, tölts le és fedezz fel egyéni eszközöket", "Discover, download, and explore model presets": "<PERSON><PERSON><PERSON> fel, tölts le és fedezz fel modell beállításokat", "Display": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Display Emoji in Call": "Emoji megjeleníté<PERSON> hívásban", "Display the username instead of You in the Chat": "Felhasználónév megjelenítése a 'Te' helyett a beszélgeté<PERSON>ben", "Displays citations in the response": "Idézetek megjelenítése a válaszban", "Dive into knowledge": "Merü<PERSON>j el a tudásban", "Do not install functions from sources you do not fully trust.": "Ne telepíts funkciókat olyan <PERSON>, amelyekben nem bízol telje<PERSON>.", "Do not install tools from sources you do not fully trust.": "Ne telepíts eszközöket olyan forrásokból, amelyekben nem bízol teljesen.", "Docling": "<PERSON><PERSON>", "Docling Server URL required.": "Docling szerver URL szükséges.", "Document": "Dokumentum", "Document Intelligence": "Dokumentum intelligencia", "Document Intelligence endpoint and key required.": "Dokumentum intelligencia végpont és kulcs szükséges.", "Documentation": "Do<PERSON>ment<PERSON><PERSON>ó", "Documents": "Dokumentumok", "does not make any external connections, and your data stays securely on your locally hosted server.": "nem létesí<PERSON> k<PERSON> ka<PERSON>olatokat, és az adataid biztonságban maradnak a helyileg hosztolt szervereden.", "Domain Filter List": "Domain szűrőlista", "Don't have an account?": "Nincs még fiókod?", "don't install random functions from sources you don't trust.": "ne telepíts véletlenszerű funkciókat olyan forrásokból, amelyekben nem bízol.", "don't install random tools from sources you don't trust.": "ne telepíts véletlenszerű eszközöket olyan forrásokból, amelyekben nem bízol.", "Don't like the style": "Nem te<PERSON>zik a stílus", "Done": "<PERSON><PERSON><PERSON>", "Download": "Letöltés", "Download as SVG": "Letöltés SVG-ként", "Download canceled": "Letöltés megszakítva", "Download Database": "Adatbázis letölt<PERSON>e", "Drag and drop a file to upload or select a file to view": "Húzz ide egy fájlt a feltöltéshez vagy válassz fájlt a megtekintéshez", "Draw": "<PERSON><PERSON><PERSON><PERSON>", "Drop any files here to upload": "", "e.g. '30s','10m'. Valid time units are 's', 'm', 'h'.": "pl. '30s','10m'. Érvényes időegységek: 's', 'm', 'h'.", "e.g. \"json\" or a JSON schema": "pl. \"json\" vagy egy JSON séma", "e.g. 60": "pl. 60", "e.g. A filter to remove profanity from text": "pl. <PERSON><PERSON> a trágárság eltávolítására a szövegből", "e.g. en": "", "e.g. My Filter": "pl. <PERSON><PERSON> <PERSON>n <PERSON>", "e.g. My Tools": "pl. Az <PERSON>n eszközeim", "e.g. my_filter": "pl. az_en_sz<PERSON><PERSON>", "e.g. my_tools": "pl. az_en_eszkozeim", "e.g. pdf, docx, txt": "", "e.g. Tools for performing various operations": "pl. Eszközök különböző műveletek elvégzéséhez", "e.g., 3, 4, 5 (leave blank for default)": "", "e.g., audio/wav,audio/mpeg,video/* (leave blank for defaults, * for all)": "", "e.g., en-US,ja-JP (leave blank for auto-detect)": "", "e.g., westus (leave blank for eastus)": "", "e.g.) en,fr,de": "", "Edit": "Szerkesztés", "Edit Arena Model": "Arena modell szerkesztése", "Edit Channel": "Csatorna s<PERSON>kesztése", "Edit Connection": "Ka<PERSON><PERSON><PERSON>t szerkesztése", "Edit Default Permissions": "Alapértelmezett engedé<PERSON>ek szerkesztése", "Edit Folder": "", "Edit Memory": "Memória szerkesztése", "Edit User": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Edit User Group": "Felhasználói csoport szerkesztése", "Edited": "", "Editing": "", "Eject": "", "ElevenLabs": "ElevenLabs", "Email": "Email", "Embark on adventures": "<PERSON><PERSON><PERSON><PERSON> <PERSON>", "Embedding": "Beágyazás", "Embedding Batch Size": "Beágyazási köteg méret", "Embedding Model": "Beágyazási modell", "Embedding Model Engine": "Beágyazási modell motor", "Embedding model set to \"{{embedding_model}}\"": "Beágyazási modell beállítva: \"{{embedding_model}}\"", "Enable API Key": "API kulcs engedélyezése", "Enable autocomplete generation for chat messages": "Automatikus kiegészítés engedélyezése csevegőüzenetekhez", "Enable Code Execution": "Kód v<PERSON>grehajtás engedélyezése", "Enable Code Interpreter": "<PERSON><PERSON><PERSON> engedélyezése", "Enable Community Sharing": "Közösségi megosztás engedélyezése", "Enable Memory Locking (mlock) to prevent model data from being swapped out of RAM. This option locks the model's working set of pages into RAM, ensuring that they will not be swapped out to disk. This can help maintain performance by avoiding page faults and ensuring fast data access.": "Engedélyezd a memória <PERSON> (mlock), hogy a modell adatai ne kerüljenek ki a RAM-ból. Ez az opció a modell munkakészletének oldalait a RAM-ban rögzíti, így nem kerülnek a lemezre. Ez segíthet a teljesítmény fenntartásában az oldalhibák elkerülésével és a gyors adathozzáférés biztosításával.", "Enable Memory Mapping (mmap) to load model data. This option allows the system to use disk storage as an extension of RAM by treating disk files as if they were in RAM. This can improve model performance by allowing for faster data access. However, it may not work correctly with all systems and can consume a significant amount of disk space.": "Engedélyezd a memória leképezést (mmap) a modell adatainak betöltéséhez. Ez az opció lehetővé teszi a rendszer számára, hogy a lemeztárolót a RAM kiterjesztéseként használja, a lemezfájlokat RAM-ban lévőként kezelve. Ez javíthatja a modell teljesítményét a gyorsabb adathozzáférés révén. Azonban nem minden rendszerrel működik megfelelően és jelentős lemezterületet foglalhat.", "Enable Message Rating": "Üzenet értékelés engedélyezése", "Enable Mirostat sampling for controlling perplexity.": "Engedélyezd a Mirostat mintavételezést a perplexitás szabályozásához.", "Enable New Sign Ups": "Új regisztrációk engedélyezése", "Enabled": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Endpoint URL": "", "Enforce Temporary Chat": "Ideiglenes csevegés kikényszerítése", "Enhance": "", "Ensure your CSV file includes 4 columns in this order: Name, Email, Password, Role.": "Győződj meg róla, hogy a CSV fájl tartalmazza ezt a 4 oszlopot ebben a sorrendben: Név, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>.", "Enter {{role}} message here": "<PERSON>rd ide a {{role}} üzenetet", "Enter a detail about yourself for your LLMs to recall": "Adj meg egy részletet magadról, amit az LLM-ek megjegyezhetnek", "Enter a title for the pending user info overlay. Leave empty for default.": "", "Enter a watermark for the response. Leave empty for none.": "", "Enter api auth string (e.g. username:password)": "Add meg az API hitelesítési karakterláncot (pl. felhasználónév:jelszó)", "Enter Application DN": "Add meg az alkalmazás DN-t", "Enter Application DN Password": "Add meg az alkalmazás DN jelszavát", "Enter Bing Search V7 Endpoint": "Add meg a Bing Search V7 végpontot", "Enter Bing Search V7 Subscription Key": "Add meg a Bing Search V7 előfizetési kulcsot", "Enter BM25 Weight": "", "Enter Bocha Search API Key": "Add meg a Bocha Search API kulcsot", "Enter Brave Search API Key": "Add meg a Brave Search API kulcsot", "Enter certificate path": "Add meg a tanúsítvány útvonalát", "Enter CFG Scale (e.g. 7.0)": "Add meg a CFG skálát (pl. 7.0)", "Enter Chunk Overlap": "Add meg a darab <PERSON>", "Enter Chunk Size": "Add meg a darab méretet", "Enter comma-separated \"token:bias_value\" pairs (example: 5432:100, 413:-100)": "Add meg vessz<PERSON><PERSON> el<PERSON>las<PERSON>tt \"token:bias_ért<PERSON>k\" párokat (példa: 5432:100, 413:-100)", "Enter Config in JSON format": "", "Enter content for the pending user info overlay. Leave empty for default.": "", "Enter Datalab Marker API Key": "", "Enter description": "Add meg a leí<PERSON>", "Enter Docling OCR Engine": "", "Enter Docling OCR Language(s)": "", "Enter Docling Server URL": "Add meg a <PERSON>ling szerver URL-t", "Enter Document Intelligence Endpoint": "Add meg a dokumentum intelligencia végpontot", "Enter Document Intelligence Key": "Add meg a dokumentum intelligencia kulcsot", "Enter domains separated by commas (e.g., example.com,site.org)": "Add meg a domaineket vesszővel elválasztva (pl. example.com,site.org)", "Enter Exa API Key": "Add meg az Exa API kulcsot", "Enter External Document Loader API Key": "", "Enter External Document Loader URL": "", "Enter External Web Loader API Key": "", "Enter External Web Loader URL": "", "Enter External Web Search API Key": "", "Enter External Web Search URL": "", "Enter Firecrawl API Base URL": "", "Enter Firecrawl API Key": "", "Enter folder name": "", "Enter Github Raw URL": "Add meg a Github Raw URL-t", "Enter Google PSE API Key": "Add meg a Google PSE API kulcsot", "Enter Google PSE Engine Id": "Add meg a Google PSE motor azonosítót", "Enter Image Size (e.g. 512x512)": "Add meg a kép méretet (pl. 512x512)", "Enter Jina API Key": "Add meg a Jina API kulcsot", "Enter Jupyter Password": "Add meg a <PERSON><PERSON><PERSON>", "Enter Jupyter Token": "Add meg a <PERSON><PERSON><PERSON> tokent", "Enter Jupyter URL": "Add meg a Jupyter URL-t", "Enter Kagi Search API Key": "Add meg a Kagi Search API kulcsot", "Enter Key Behavior": "Add meg a kulcs viselkedés<PERSON>t", "Enter language codes": "Add meg a nyelvi kódokat", "Enter Mistral API Key": "Add meg a Mistral API kulcsot", "Enter Model ID": "Add meg a modell a<PERSON>", "Enter model tag (e.g. {{modelTag}})": "Add meg a modell cím<PERSON>t (pl. {{modelTag}})", "Enter Mojeek Search API Key": "Add meg a Mojeek Search API kulcsot", "Enter name": "", "Enter New Password": "", "Enter Number of Steps (e.g. 50)": "Add meg a lépések szá<PERSON>t (pl. 50)", "Enter Perplexity API Key": "Add meg a Perplexity API kulcsot", "Enter Playwright Timeout": "", "Enter Playwright WebSocket URL": "", "Enter proxy URL (e.g. **************************:port)": "Add meg a proxy URL-t (pl. **************************:port)", "Enter reasoning effort": "Add meg az érvelési erőfeszítést", "Enter Sampler (e.g. Euler a)": "Add meg a mintavételezőt (pl. Euler a)", "Enter Scheduler (e.g. Karras)": "Add meg az ü<PERSON> (pl. <PERSON><PERSON><PERSON>)", "Enter Score": "Add meg a pontszámot", "Enter SearchApi API Key": "Add meg a SearchApi API kulcsot", "Enter SearchApi Engine": "Add meg a SearchApi motort", "Enter Searxng Query URL": "Add meg a Searxng lekérdezési URL-t", "Enter Seed": "Add meg a seed értéket", "Enter SerpApi API Key": "Add meg a SerpApi API kulcsot", "Enter SerpApi Engine": "Add meg a SerpApi motort", "Enter Serper API Key": "Add meg a Serper API kulcsot", "Enter Serply API Key": "Add meg a Serply API kulcsot", "Enter Serpstack API Key": "Add meg a Serpstack API kulcsot", "Enter server host": "Add meg a szerver hosztot", "Enter server label": "Add meg a szerver cím<PERSON>t", "Enter server port": "Add meg a szerver portot", "Enter Sougou Search API sID": "", "Enter Sougou Search API SK": "", "Enter stop sequence": "Add meg a leállítási szekvenciát", "Enter system prompt": "Add meg a rendszer promptot", "Enter system prompt here": "<PERSON>rd ide a rendszer promptot", "Enter Tavily API Key": "Add meg a Tavily API kulcsot", "Enter Tavily Extract Depth": "", "Enter the public URL of your WebUI. This URL will be used to generate links in the notifications.": "Add meg a WebUI nyilvános URL-jét. Ez az URL lesz használva az értesítésekben lévő linkek generálásához.", "Enter the URL of the function to import": "", "Enter the URL to import": "", "Enter Tika Server URL": "Add meg a Tika szerver URL-t", "Enter timeout in seconds": "Add meg az időtúllépést másodpercekben", "Enter to Send": "Enter a küldéshez", "Enter Top K": "Add meg a Top K értéket", "Enter Top K Reranker": "Add meg a Top K újrarangsorolót", "Enter URL (e.g. http://127.0.0.1:7860/)": "Add meg az URL-t (pl. http://127.0.0.1:7860/)", "Enter URL (e.g. http://localhost:11434)": "Add meg az URL-t (pl. http://localhost:11434)", "Enter Yacy Password": "", "Enter Yacy URL (e.g. http://yacy.example.com:8090)": "", "Enter Yacy Username": "", "Enter your current password": "Add meg a jelenlegi j<PERSON>d", "Enter Your Email": "Add meg az email címed", "Enter Your Full Name": "Add meg a teljes neved", "Enter your message": "Írd be az üzeneted", "Enter your name": "Add meg a neved", "Enter Your Name": "", "Enter your new password": "Add meg az új j<PERSON>d", "Enter Your Password": "Add meg a j<PERSON>d", "Enter Your Role": "Add meg a szereped", "Enter Your Username": "Add meg a felhasználóneved", "Enter your webhook URL": "Add meg a webhook URL-t", "Error": "Hiba", "ERROR": "HIBA", "Error accessing Google Drive: {{error}}": "Hiba a Google Drive elérése során: {{error}}", "Error accessing media devices.": "", "Error starting recording.": "", "Error unloading model: {{error}}": "", "Error uploading file: {{error}}": "Hiba a fájl feltöltése során: {{error}}", "Evaluations": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Everyone": "", "Exa API Key": "Exa API kulcs", "Example: (&(objectClass=inetOrgPerson)(uid=%s))": "Példa: (&(objectClass=inetOrgPerson)(uid=%s))", "Example: ALL": "Példa: MIND", "Example: mail": "<PERSON><PERSON><PERSON>: email", "Example: ou=users,dc=foo,dc=example": "Példa: ou=users,dc=foo,dc=example", "Example: sAMAccountName or uid or userPrincipalName": "<PERSON><PERSON>lda: sAMAccountName vagy uid vagy userPrincipalName", "Exceeded the number of seats in your license. Please contact support to increase the number of seats.": "T<PERSON>lépted a licencben lévő helyek számát. <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, vedd fel a kapcsolatot a támogatással a helyek számának növeléséhez.", "Exclude": "Kizárás", "Execute code for analysis": "Kód végrehajtása elemzéshez", "Executing **{{NAME}}**...": "**{{NAME}}** végrehajtása...", "Expand": "Kibontás", "Experimental": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Explain": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Explore the cosmos": "Fedezd fel a kozmoszt", "Export": "Exportálás", "Export All Archived Chats": "Minden archivált csevegés exportálása", "Export All Chats (All Users)": "Minden beszélgetés exportálása (minden felhasználó)", "Export chat (.json)": "Beszélgetés exportálása (.json)", "Export Chats": "Beszélgetések exportálása", "Export Config to JSON File": "Konfiguráció exportálása JSON fájlba", "Export Functions": "Funkciók exportálása", "Export Models": "Modellek exportálása", "Export Presets": "<PERSON><PERSON><PERSON>", "Export Prompt Suggestions": "", "Export Prompts": "Promptok exportálása", "Export to CSV": "Exportálás CSV-be", "Export Tools": "Eszközök exportálása", "External": "<PERSON><PERSON><PERSON><PERSON>", "External Document Loader URL required.": "", "External Task Model": "", "External Web Loader API Key": "", "External Web Loader URL": "", "External Web Search API Key": "", "External Web Search URL": "", "Failed to add file.": "<PERSON><PERSON> hozz<PERSON>adni a fájlt.", "Failed to connect to {{URL}} OpenAPI tool server": "<PERSON><PERSON> c<PERSON>lak<PERSON>ni a {{URL}} OpenAPI eszköszerverhez", "Failed to copy link": "", "Failed to create API Key.": "<PERSON>em si<PERSON>lt létrehozni az API kulcsot.", "Failed to delete note": "", "Failed to extract content from the file: {{error}}": "", "Failed to extract content from the file.": "", "Failed to fetch models": "<PERSON><PERSON> a modelleket", "Failed to generate title": "", "Failed to load file content.": "", "Failed to read clipboard contents": "<PERSON><PERSON> o<PERSON> a vágólap tartalmát", "Failed to save connections": "<PERSON><PERSON>teni a kapcsolatokat", "Failed to save models configuration": "<PERSON><PERSON>ten<PERSON> a modellek konfigurációját", "Failed to update settings": "<PERSON><PERSON> f<PERSON>íteni a beállításokat", "Failed to upload file.": "<PERSON><PERSON> a fájlt.", "Features": "Funkciók", "Features Permissions": "Funkciók engedélyei", "February": "<PERSON><PERSON><PERSON><PERSON>", "Feedback Details": "", "Feedback History": "Visszajelzés előzmények", "Feedbacks": "Visszajelzések", "Feel free to add specific details": "Nyugodtan adj hozzá specifikus részleteket", "File": "<PERSON><PERSON><PERSON><PERSON>", "File added successfully.": "<PERSON><PERSON><PERSON><PERSON> ho<PERSON>.", "File content updated successfully.": "Fájl tartalom sikeresen frissítve.", "File Mode": "<PERSON><PERSON><PERSON><PERSON> mód", "File not found.": "Fájl nem található.", "File removed successfully.": "<PERSON><PERSON><PERSON><PERSON>távolít<PERSON>.", "File size should not exceed {{maxSize}} MB.": "A fájl mérete nem haladhatja meg a {{maxSize}} MB-ot.", "File Upload": "", "File uploaded successfully": "<PERSON><PERSON><PERSON><PERSON>", "Files": "Fájlok", "Filter": "", "Filter is now globally disabled": "A szűrő globálisan letiltva", "Filter is now globally enabled": "A szűrő globálisan engedélyezve", "Filters": "Szűrők", "Fingerprint spoofing detected: Unable to use initials as avatar. Defaulting to default profile image.": "Ujjlenyomat hamisítás észlelve: Nem lehet a kezdőbetűket avatárként használni. Alapértelmezett profilkép has<PERSON>.", "Firecrawl API Base URL": "", "Firecrawl API Key": "", "Fluidly stream large external response chunks": "Nagy külső válasz darabok folyamatos streamelése", "Focus chat input": "Chat bevitel fókuszálása", "Folder deleted successfully": "Mappa si<PERSON>esen tö<PERSON>", "Folder Name": "", "Folder name cannot be empty.": "A mappa neve nem lehet üres.", "Folder name updated successfully": "Mappa neve sikeresen frissítve", "Folder updated successfully": "", "Follow up": "", "Follow Up Generation": "", "Follow Up Generation Prompt": "", "Follow-Up Auto-Generation": "", "Followed instructions perfectly": "Tökéletesen követte az utasításokat", "Force OCR": "", "Force OCR on all pages of the PDF. This can lead to worse results if you have good text in your PDFs. Defaults to False.": "", "Forge new paths": "Új utak kovácsolása", "Form": "Űrlap", "Format your variables using brackets like this:": "Formázd a változóidat zárójelekkel így:", "Forwards system user session credentials to authenticate": "Továbbítja a rendszer felhasználói munkamenet hitelesítő adatait a hitelesítéshez", "Full Context Mode": "<PERSON><PERSON><PERSON> konte<PERSON> mód", "Function": "Funkció", "Function Calling": "Funkcióhívás", "Function created successfully": "<PERSON><PERSON><PERSON>", "Function deleted successfully": "<PERSON><PERSON><PERSON> si<PERSON> tö<PERSON>", "Function Description": "<PERSON><PERSON><PERSON>", "Function ID": "<PERSON><PERSON>ó <PERSON>", "Function imported successfully": "", "Function is now globally disabled": "A funkció globális<PERSON> let<PERSON>", "Function is now globally enabled": "A funkció globálisan engedélyezve", "Function Name": "Funkció neve", "Function updated successfully": "<PERSON><PERSON><PERSON> si<PERSON>n frissítve", "Functions": "Funkciók", "Functions allow arbitrary code execution.": "A funkciók tetszőleges kód végrehajtását teszik lehetővé.", "Functions imported successfully": "Funkciók sikeresen importálva", "Gemini": "Gemini", "Gemini API Config": "Gemini API konfiguráció", "Gemini API Key is required.": "Gemini API kulcs szükséges.", "General": "<PERSON><PERSON>lán<PERSON>", "Generate": "", "Generate an image": "<PERSON><PERSON><PERSON>", "Generate Image": "<PERSON><PERSON><PERSON>", "Generate prompt pair": "Prompt pár gene<PERSON>", "Generating search query": "Keresési lekérdezés generálása", "Generating...": "", "Get information on {{name}} in the UI": "", "Get started": "Kezdj neki", "Get started with {{WEBUI_NAME}}": "Kezdj neki a {{WEBUI_NAME}}-val", "Global": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Good Response": "<PERSON><PERSON>", "Google Drive": "Google Drive", "Google PSE API Key": "Google PSE API kulcs", "Google PSE Engine Id": "Google PSE motor azonosító", "Group created successfully": "Csoport sikeresen létrehozva", "Group deleted successfully": "Csoport sikeresen törölve", "Group Description": "Csoport leírása", "Group Name": "Csoport neve", "Group updated successfully": "Csoport sikeresen frissítve", "Groups": "Csoportok", "H1": "", "H2": "", "H3": "", "Haptic Feedback": "Tapintási visszajelzés", "Hello, {{name}}": "<PERSON><PERSON>, {{name}}", "Help": "Seg<PERSON><PERSON><PERSON>g", "Help us create the best community leaderboard by sharing your feedback history!": "Segíts nekünk a legjobb közösségi ranglista létrehozásában a visszajelzési előzményeid megosztásával!", "Hex Color": "Hexa <PERSON>ín", "Hex Color - Leave empty for default color": "Hexa szín - Hagyd üresen az alapértelmezett színhez", "Hide": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Hide from Sidebar": "", "Hide Model": "<PERSON><PERSON>", "High Contrast Mode": "", "Home": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Host": "Hoszt", "How can I help you today?": "<PERSON>gyan segíthetek ma?", "How would you rate this response?": "Hogyan értékelnéd ezt a választ?", "HTML": "", "Hybrid Search": "<PERSON><PERSON><PERSON>", "I acknowledge that I have read and I understand the implications of my action. I am aware of the risks associated with executing arbitrary code and I have verified the trustworthiness of the source.": "Elismere<PERSON>, hogy elolvastam és megértem a cselekedetem következményeit. Tisztában vagyok a tetszőleges kód végrehajtásával jár<PERSON> k<PERSON>, és ellenőriztem a forrás megbízhatóságát.", "ID": "Azonosító", "iframe Sandbox Allow Forms": "", "iframe Sandbox Allow Same Origin": "", "Ignite curiosity": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> f<PERSON>", "Image": "<PERSON><PERSON><PERSON>", "Image Compression": "Képtömör<PERSON><PERSON>s", "Image Compression Height": "", "Image Compression Width": "", "Image Generation": "Képgenerá<PERSON>ás", "Image Generation (Experimental)": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (kísérleti)", "Image Generation Engine": "Képgenerálási motor", "Image Max Compression Size": "<PERSON><PERSON><PERSON> maxim<PERSON><PERSON> tömörí<PERSON>si mérete", "Image Max Compression Size height": "", "Image Max Compression Size width": "", "Image Prompt Generation": "<PERSON><PERSON><PERSON> prompt gene<PERSON><PERSON><PERSON><PERSON>", "Image Prompt Generation Prompt": "<PERSON><PERSON><PERSON> prompt <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> prompt", "Image Settings": "<PERSON><PERSON><PERSON>", "Images": "Képek", "Import": "", "Import Chats": "Beszélgetések importálása", "Import Config from JSON File": "Konfiguráció importálása JSON fájlból", "Import From Link": "", "Import Functions": "Funkciók importálása", "Import Models": "Modellek importálása", "Import Notes": "", "Import Presets": "<PERSON><PERSON><PERSON>", "Import Prompt Suggestions": "", "Import Prompts": "Promptok importálása", "Import Tools": "Eszközök importálása", "Include": "Tartalmaz", "Include `--api-auth` flag when running stable-diffusion-webui": "Add hozzá a `--api-auth` kapcsolót a stable-diffusion-webui futtatásakor", "Include `--api` flag when running stable-diffusion-webui": "Add hozzá a `--api` kapcsolót a stable-diffusion-webui futtatásakor", "Influences how quickly the algorithm responds to feedback from the generated text. A lower learning rate will result in slower adjustments, while a higher learning rate will make the algorithm more responsive.": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, hogy az algoritmus milyen gyorsan reagál a generált szöveg visszajelzéseire. Alacsonyabb tanulási ráta lassabb调整okat ered<PERSON>, míg magasabb ráta gyorsabb reagálást biztosít.", "Info": "Információ", "Inject the entire content as context for comprehensive processing, this is recommended for complex queries.": "Az összes tartalom kontextusként való befecskendezése átfogó feldolgozáshoz, ez összetett lekérdezésekhez a<PERSON>lott.", "Input commands": "<PERSON><PERSON><PERSON><PERSON> para<PERSON>", "Input Variables": "", "Insert": "", "Insert Prompt as Rich Text": "", "Install from Github URL": "Telepítés Github URL-ről", "Instant Auto-Send After Voice Transcription": "Azonnali automatikus küldés hangfelismerés után", "Integration": "Integráció", "Interface": "<PERSON><PERSON><PERSON><PERSON>", "Invalid file content": "", "Invalid file format.": "Érvénytelen f<PERSON>lf<PERSON>.", "Invalid JSON file": "", "Invalid Tag": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "is typing...": "ír...", "Italic": "", "January": "<PERSON><PERSON><PERSON><PERSON>", "Jina API Key": "Jina API kulcs", "join our Discord for help.": "Csatlakozz a Discord szerverünkhöz segítségért.", "JSON": "JSON", "JSON Preview": "JSON előnézet", "July": "<PERSON><PERSON><PERSON>", "June": "<PERSON><PERSON><PERSON>", "Jupyter Auth": "<PERSON><PERSON><PERSON>", "Jupyter URL": "Jupyter URL", "JWT Expiration": "JWT lejárat", "JWT Token": "JWT token", "Kagi Search API Key": "Kagi Search API kulcs", "Keep in Sidebar": "", "Key": "<PERSON><PERSON><PERSON>", "Keyboard shortcuts": "Billentyűpar<PERSON>ok", "Knowledge": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Knowledge Access": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Knowledge Base": "", "Knowledge created successfully.": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>.", "Knowledge deleted successfully.": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> si<PERSON> tö<PERSON>.", "Knowledge Public Sharing": "Tudásb<PERSON><PERSON>s nyilvános megosztása", "Knowledge reset successfully.": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> si<PERSON> v<PERSON>llítva.", "Knowledge updated successfully": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> si<PERSON>n frissítve", "Kokoro.js (Browser)": "Kokoro.js (Böngésző)", "Kokoro.js Dtype": "Kokoro.js Dtype", "Label": "<PERSON><PERSON><PERSON><PERSON>", "Landing Page Mode": "<PERSON><PERSON><PERSON><PERSON><PERSON> mód", "Language": "Nyelv", "Language Locales": "", "Languages": "", "Last Active": "Utoljára a<PERSON>ív", "Last Modified": "U<PERSON><PERSON><PERSON><PERSON><PERSON>", "Last reply": "Utolsó v<PERSON>lasz", "LDAP": "LDAP", "LDAP server updated": "LDAP szerver frissítve", "Leaderboard": "<PERSON><PERSON><PERSON><PERSON>", "Learn more about OpenAPI tool servers.": "Tudj meg többet az OpenAPI eszköszerverekről.", "Leave empty for no compression": "", "Leave empty for unlimited": "Hagyja üresen a korlátlan használathoz", "Leave empty to include all models from \"{{url}}\" endpoint": "", "Leave empty to include all models from \"{{url}}/api/tags\" endpoint": "Hagyja üresen, hogy az összes modellt tartalmazza a \"{{url}}/api/tags\" végpontból", "Leave empty to include all models from \"{{url}}/models\" endpoint": "Hagyja üresen, hogy az összes modellt tartalmazza a \"{{url}}/models\" végpontból", "Leave empty to include all models or select specific models": "Hagyja üresen az összes modell hasz<PERSON>z, vagy v<PERSON> ki konkrét modelleket", "Leave empty to use the default prompt, or enter a custom prompt": "Hagyja üresen az alapértelmezett prompt <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, vagy adjon meg egyéni promptot", "Leave model field empty to use the default model.": "Hagyja üresen a modell mezőt az alapértelmezett modell használatához.", "License": "Licenc", "Light": "<PERSON>il<PERSON><PERSON>", "Listening...": "Hallgatás...", "Llama.cpp": "Llama.cpp", "LLMs can make mistakes. Verify important information.": "Az LLM-ek hibázhatnak. Ellenőrizze a fontos információkat.", "Loader": "Betöltő", "Loading Kokoro.js...": "Kokoro.js betöltése...", "Local": "<PERSON><PERSON><PERSON>", "Local Task Model": "", "Location access not allowed": "Helyhozzáférés nem engedély<PERSON>ett", "Lost": "Elveszett", "LTR": "LTR", "Made by Open WebUI Community": "Az OpenWebUI közösség által készítve", "Make password visible in the user interface": "", "Make sure to enclose them with": "Győződjön meg róla, hogy körülveszi <PERSON>", "Make sure to export a workflow.json file as API format from ComfyUI.": "Győződjön meg róla, hogy exportál egy workflow.json fájlt API formátumban a ComfyUI-ból.", "Manage": "Kezelés", "Manage Direct Connections": "Közvetlen kapcsolatok kezelése", "Manage Models": "Modellek kezelése", "Manage Ollama": "<PERSON><PERSON><PERSON> k<PERSON>", "Manage Ollama API Connections": "Ollama API kapcsolatok kezelése", "Manage OpenAI API Connections": "OpenAI API kapcsolatok kezelése", "Manage Pipelines": "Folyamatok k<PERSON>", "Manage Tool Servers": "Eszközszerverek kez<PERSON>", "March": "<PERSON><PERSON><PERSON><PERSON>", "Markdown": "", "Markdown (Header)": "", "Max Speakers": "", "Max Upload Count": "Maximum feltöltések száma", "Max Upload Size": "Maximum feltöltési méret", "Maximum of 3 models can be downloaded simultaneously. Please try again later.": "Maximum 3 modell tölthető le egyszerre. <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, prób<PERSON><PERSON><PERSON> k<PERSON>.", "May": "<PERSON><PERSON><PERSON><PERSON>", "Memories accessible by LLMs will be shown here.": "Az LLM-ek által elérhető emlékek itt jelennek meg.", "Memory": "Memória", "Memory added successfully": "<PERSON><PERSON><PERSON><PERSON> ho<PERSON>", "Memory cleared successfully": "Memória si<PERSON>n tö<PERSON>", "Memory deleted successfully": "Memória si<PERSON>n tö<PERSON>", "Memory updated successfully": "Memória si<PERSON>n frissítve", "Merge Responses": "Válaszok egyesítése", "Merged Response": "Összevont v<PERSON>lasz", "Message rating should be enabled to use this feature": "Az üzenetértékelésnek engedélyezve kell lennie ehhez a funkcióhoz", "Messages you send after creating your link won't be shared. Users with the URL will be able to view the shared chat.": "A link létrehozása után küldött üzenetei nem lesznek megosztva. A URL-lel rendelkező felhasználók megtekinthetik a megosztott beszélgetést.", "Microsoft OneDrive": "", "Microsoft OneDrive (personal)": "", "Microsoft OneDrive (work/school)": "", "Mistral OCR": "Mistral OCR", "Mistral OCR API Key required.": "Mistral OCR API kulcs szükséges.", "Model": "<PERSON><PERSON>", "Model '{{modelName}}' has been successfully downloaded.": "A '{{modelName}}' modell sikeresen letöltve.", "Model '{{modelTag}}' is already in queue for downloading.": "A '{{modelTag}}' modell már a letöltési sorban van.", "Model {{modelId}} not found": "A {{modelId}} modell nem ta<PERSON>", "Model {{modelName}} is not vision capable": "A {{modelName}} modell nem ké<PERSON> k<PERSON>pfeldolgozásra", "Model {{name}} is now {{status}}": "A {{name}} modell most {{status}} állapotban van", "Model {{name}} is now hidden": "A {{name}} modell most elrejtve", "Model {{name}} is now visible": "A {{name}} modell most látható", "Model accepts file inputs": "", "Model accepts image inputs": "A modell elfogad képbemenetet", "Model can execute code and perform calculations": "", "Model can generate images based on text prompts": "", "Model can search the web for information": "", "Model created successfully!": "<PERSON>l si<PERSON>esen létrehozva!", "Model filesystem path detected. Model shortname is required for update, cannot continue.": "Modell fájlrendszer útvonal észlelve. A modell rövid neve szükséges a frissítéshez, nem folytatható.", "Model Filtering": "Modellszű<PERSON><PERSON>", "Model ID": "<PERSON><PERSON>", "Model IDs": "<PERSON><PERSON>", "Model Name": "<PERSON>l neve", "Model not selected": "<PERSON><PERSON><PERSON> kiválasztva modell", "Model Params": "<PERSON><PERSON>", "Model Permissions": "<PERSON><PERSON>", "Model unloaded successfully": "", "Model updated successfully": "<PERSON><PERSON> f<PERSON>", "Model(s) do not support file upload": "", "Modelfile Content": "<PERSON><PERSON><PERSON><PERSON><PERSON> tartalo<PERSON>", "Models": "<PERSON>le<PERSON>", "Models Access": "<PERSON><PERSON>k ho<PERSON>áférése", "Models configuration saved successfully": "Modellek konfigurációja sikeresen mentve", "Models Public Sharing": "Modellek nyilvános megosztása", "Mojeek Search API Key": "Mojeek Search API kulcs", "more": "t<PERSON>bb", "More": "<PERSON><PERSON><PERSON>", "Name": "Név", "Name your knowledge base": "Nevezd el a tudásbázisodat", "Native": "Natív", "New Chat": "<PERSON><PERSON>", "New Folder": "<PERSON><PERSON> <PERSON><PERSON>", "New Function": "", "New Note": "", "New Password": "<PERSON><PERSON>", "New Tool": "", "new-channel": "<PERSON><PERSON>", "Next message": "", "No chats found for this user.": "", "No chats found.": "", "No content": "", "No content found": "<PERSON><PERSON> tarta<PERSON>", "No content found in file.": "", "No content to speak": "<PERSON><PERSON><PERSON> felo<PERSON><PERSON><PERSON><PERSON> tarta<PERSON>", "No distance available": "<PERSON><PERSON><PERSON>r<PERSON> távolság", "No feedbacks found": "<PERSON><PERSON> v<PERSON>", "No file selected": "<PERSON><PERSON><PERSON> k<PERSON>lasztva fájl", "No groups with access, add a group to grant access": "<PERSON><PERSON><PERSON><PERSON> rendelkez<PERSON> csoport, adj hozz<PERSON> egy csoportot a hozzáférés megadásához", "No HTML, CSS, or JavaScript content found.": "<PERSON>em <PERSON> HTML, CSS vagy JavaScript tartalom.", "No inference engine with management support found": "<PERSON><PERSON> k<PERSON>t támogató következtetési motor", "No knowledge found": "<PERSON><PERSON> t<PERSON>", "No memories to clear": "<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON>ó<PERSON>", "No model IDs": "<PERSON><PERSON><PERSON> <PERSON>", "No models found": "<PERSON><PERSON>l", "No models selected": "<PERSON><PERSON><PERSON> modell", "No Notes": "", "No results found": "<PERSON><PERSON><PERSON>", "No search query generated": "<PERSON><PERSON> keresési le<PERSON>é<PERSON>z<PERSON>", "No source available": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "No users were found.": "<PERSON><PERSON> f<PERSON>.", "No valves to update": "<PERSON><PERSON><PERSON> f<PERSON><PERSON><PERSON><PERSON> szelep", "None": "<PERSON><PERSON><PERSON>", "Not factually correct": "Tényszerűen nem helyes", "Not helpful": "<PERSON><PERSON>", "Note deleted successfully": "", "Note: If you set a minimum score, the search will only return documents with a score greater than or equal to the minimum score.": "Megjegyzés: Ha minimum pontszámot <PERSON> be, a keresés csak olyan dokumentumokat ad vissza, am<PERSON><PERSON> pontszáma nagyobb vagy egy<PERSON>ő a minimum pontszámmal.", "Notes": "Jegyzetek", "Notification Sound": "Értesítési hang", "Notification Webhook": "Értesítési webhook", "Notifications": "Értesítések", "November": "November", "OAuth ID": "<PERSON><PERSON><PERSON>", "October": "Október", "Off": "<PERSON>", "Okay, Let's Go!": "Rend<PERSON>, kezd<PERSON><PERSON><PERSON>!", "OLED Dark": "OLED sötét", "Ollama": "Ollama", "Ollama API": "Ollama API", "Ollama API settings updated": "Ollama API beállítások frissítve", "Ollama Version": "<PERSON><PERSON><PERSON> verzi<PERSON>", "On": "Be", "OneDrive": "OneDrive", "Only active when \"Paste Large Text as File\" setting is toggled on.": "", "Only active when the chat input is in focus and an LLM is generating a response.": "", "Only alphanumeric characters and hyphens are allowed": "Csak alfanumerikus karakterek és kötőjelek engedélyezettek", "Only alphanumeric characters and hyphens are allowed in the command string.": "Csak alfanumerikus karakterek és kötőjelek engedélyezettek a parancssorban.", "Only collections can be edited, create a new knowledge base to edit/add documents.": "Csak gyűjtemények szerkeszthetők, hozzon létre új tudásbázist dokumentumok szerkesztéséhez/hozzáadásához.", "Only markdown files are allowed": "", "Only select users and groups with permission can access": "Csak a kiválasztott, engedéllyel rendelkező felhasználók és csoportok férhetnek hozzá", "Oops! Looks like the URL is invalid. Please double-check and try again.": "Hoppá! Úgy tűnik, az URL érvénytelen. K<PERSON><PERSON><PERSON><PERSON>k, ellenő<PERSON>ze és próbálja újra.", "Oops! There are files still uploading. Please wait for the upload to complete.": "Hoppá! Még vannak feltöltés alatt álló fájlok. <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, várja meg a feltöltés befejezését.", "Oops! There was an error in the previous response.": "Hoppá! Hiba történt az előző válaszban.", "Oops! You're using an unsupported method (frontend only). Please serve the WebUI from the backend.": "Hoppá! Nem t<PERSON>mo<PERSON>ott módszert használ (csak frontend). <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, szolgálja ki a WebUI-t a backend-ről.", "Open file": "<PERSON><PERSON><PERSON><PERSON>", "Open in full screen": "Megnyitás teljes k<PERSON>n", "Open modal to configure connection": "", "Open new chat": "<PERSON><PERSON>", "Open WebUI can use tools provided by any OpenAPI server.": "Az Open WebUI bármely OpenAPI szerver által biztosított eszközöket használhat.", "Open WebUI uses faster-whisper internally.": "Az Open WebUI belsőleg a faster-whispert használja.", "Open WebUI uses SpeechT5 and CMU Arctic speaker embeddings.": "Az Open WebUI a SpeechT5-öt és a CMU Arctic hangszóró beágyazásokat használja.", "Open WebUI version (v{{OPEN_WEBUI_VERSION}}) is lower than required version (v{{REQUIRED_VERSION}})": "Az Open WebUI verzió (v{{OPEN_WEBUI_VERSION}}) alacsonyabb, mint a szükséges verzió (v{{REQUIRED_VERSION}})", "OpenAI": "OpenAI", "OpenAI API": "OpenAI API", "OpenAI API Config": "OpenAI API konfiguráció", "OpenAI API Key is required.": "OpenAI API kulcs szükséges.", "OpenAI API settings updated": "OpenAI API beállítások frissítve", "OpenAI URL/Key required.": "OpenAI URL/kulcs szükséges.", "openapi.json URL or Path": "", "Options for running a local vision-language model in the picture description. The parameters refer to a model hosted on Hugging Face. This parameter is mutually exclusive with picture_description_api.": "", "or": "vagy", "Ordered List": "", "Organize your users": "Szervezd meg a felhasználóidat", "Other": "<PERSON><PERSON><PERSON><PERSON>", "OUTPUT": "KIMENET", "Output format": "<PERSON><PERSON><PERSON>", "Output Format": "", "Overview": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "page": "oldal", "Paginate": "", "Parameters": "", "Password": "Je<PERSON><PERSON><PERSON>", "Paste Large Text as File": "Nagy szöveg beillesztése fájlként", "PDF document (.pdf)": "PDF dokumentum (.pdf)", "PDF Extract Images (OCR)": "PDF képek kinyerése (OCR)", "pending": "függőben", "Pending": "", "Pending User Overlay Content": "", "Pending User Overlay Title": "", "Permission denied when accessing media devices": "Hozzáférés megtagadva a médiaeszközökhöz", "Permission denied when accessing microphone": "Hozzáférés megtagadva a mikrofonhoz", "Permission denied when accessing microphone: {{error}}": "Hozzáfé<PERSON>s megtagadva a mikrofonhoz: {{error}}", "Permissions": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Perplexity API Key": "Perplexity API kulcs", "Perplexity Model": "", "Perplexity Search Context Usage": "", "Personalization": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Picture Description API Config": "", "Picture Description Local Config": "", "Picture Description Mode": "", "Pin": "Rög<PERSON><PERSON><PERSON><PERSON>", "Pinned": "Rögzítve", "Pioneer insights": "Úttörő betekintések", "Pipe": "", "Pipeline deleted successfully": "<PERSON><PERSON><PERSON><PERSON> si<PERSON>n tö<PERSON>", "Pipeline downloaded successfully": "<PERSON><PERSON><PERSON><PERSON> si<PERSON>n letöltve", "Pipelines": "Folyamatok", "Pipelines Not Detected": "Folyamatok nem észlelhetők", "Pipelines Valves": "<PERSON><PERSON><PERSON><PERSON>", "Plain text (.md)": "", "Plain text (.txt)": "Egyszerű szöveg (.txt)", "Playground": "J<PERSON>tszó<PERSON>r", "Playwright Timeout (ms)": "", "Playwright WebSocket URL": "", "Please carefully review the following warnings:": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, gondosan tekintse át a következő figyelmeztetéseket:", "Please do not close the settings page while loading the model.": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ne zárja be a beállítások oldalt a modell betöltése közben.", "Please enter a prompt": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, adjon meg egy promptot", "Please enter a valid path": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, adjon meg egy érvényes útvonalat", "Please enter a valid URL": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, adjon meg egy érvényes URL-t", "Please fill in all fields.": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, töltse ki az összes mezőt.", "Please select a model first.": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, először válasszon egy modellt.", "Please select a model.": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, v<PERSON>lasszon egy modellt.", "Please select a reason": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, v<PERSON>lasszon egy okot", "Port": "Port", "Positive attitude": "Pozitív hozzáállás", "Prefix ID": "Előtag azonosító", "Prefix ID is used to avoid conflicts with other connections by adding a prefix to the model IDs - leave empty to disable": "Az előtag azonosító a modell azonosítókhoz hozzáadott előtag segítségével elkerüli az egyéb kapcsolatokkal való ütközéseket - hagyja üresen a letiltáshoz", "Prevent file creation": "", "Preview": "", "Previous 30 days": "Előző 30 nap", "Previous 7 days": "Előző 7 nap", "Previous message": "", "Private": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Profile Image": "Profilkép", "Prompt": "Prompt", "Prompt (e.g. Tell me a fun fact about the Roman Empire)": "Prompt (pl. <PERSON><PERSON><PERSON> egy érdekes tényt a Római Birodalomról)", "Prompt Autocompletion": "Prompt automatikus kiegészítés", "Prompt Content": "Prompt tartalom", "Prompt created successfully": "Prompt si<PERSON><PERSON><PERSON>", "Prompt suggestions": "Prompt javaslatok", "Prompt updated successfully": "Prompt si<PERSON>esen fris<PERSON>ve", "Prompts": "Promptok", "Prompts Access": "Promptok hozzáférése", "Prompts Public Sharing": "Promptok nyilvános megosztása", "Public": "N<PERSON>lván<PERSON>", "Pull \"{{searchValue}}\" from Ollama.com": "\"{{searchValue}}\" letöltése az Ollama.com-ról", "Pull a model from Ollama.com": "Modell letöltése az Ollama.com-ról", "Query Generation Prompt": "Lekérdezés generá<PERSON>ási prompt", "RAG Template": "RAG sablon", "Rating": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Re-rank models by topic similarity": "Modellek <PERSON>jrarangsorolása téma has<PERSON>lóság alapján", "Read": "<PERSON><PERSON><PERSON><PERSON>", "Read Aloud": "Fe<PERSON>lvas<PERSON>", "Reason": "", "Reasoning Effort": "Érvelési <PERSON>", "Record": "", "Record voice": "<PERSON> rögzí<PERSON>", "Redirecting you to Open WebUI Community": "Átirányítás az OpenWebUI közösséghez", "Reduces the probability of generating nonsense. A higher value (e.g. 100) will give more diverse answers, while a lower value (e.g. 10) will be more conservative.": "Csökkenti a ostobaság generálásának valószínűségét. Magasabb érték (pl. 100) változatosabb válaszokat ad, míg alacsonyabb érték (pl. 10) konzervatívabb lesz.", "Refer to yourself as \"User\" (e.g., \"User is learning Spanish\")": "Hivatkozzon magára \"Felhaszná<PERSON>ó\"-k<PERSON>t (pl. \"A Felhasználó spanyolul tanul\")", "References from": "Hivatkozások innen", "Refused when it shouldn't have": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, amikor nem kellett volna", "Regenerate": "Újragenerálás", "Reindex": "", "Reindex Knowledge Base Vectors": "", "Release Notes": "Kiadási jegyzetek", "Releases": "", "Relevance": "Relevancia", "Relevance Threshold": "", "Remember Dismissal": "", "Remove": "Eltávolítás", "Remove {{MODELID}} from list.": "", "Remove file": "", "Remove File": "", "Remove image": "", "Remove Model": "<PERSON><PERSON> eltávolítás<PERSON>", "Remove this tag from list": "", "Rename": "Átnevezés", "Reorder Models": "Modellek átrendezése", "Reply in Thread": "V<PERSON>lasz sz<PERSON>lban", "Reranking Engine": "", "Reranking Model": "Újrarangsoroló modell", "Reset": "Visszaállítás", "Reset All Models": "Minden modell vissza<PERSON>llí<PERSON>ása", "Reset Upload Directory": "Feltöltési könyvtár visszaállítása", "Reset Vector Storage/Knowledge": "Vektor tá<PERSON><PERSON>/tud<PERSON><PERSON><PERSON><PERSON><PERSON> v<PERSON>", "Reset view": "Nézet v<PERSON>zaállítása", "Response": "", "Response notifications cannot be activated as the website permissions have been denied. Please visit your browser settings to grant the necessary access.": "A válasz értesítések nem aktiválhatók, mert a weboldal engedélyei meg lettek tagadva. <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> el a böngésző beállításaihoz a szükséges hozzáférés megadásához.", "Response splitting": "V<PERSON>lasz felosztás", "Response Watermark": "", "Result": "<PERSON><PERSON><PERSON><PERSON>", "Retrieval": "Visszakeresés", "Retrieval Query Generation": "Visszakeresési lekérdezés generálása", "Rich Text Input for Chat": "Formázott szövegbevitel a chathez", "RK": "RK", "Role": "Szerep", "Rosé Pine": "<PERSON><PERSON><PERSON>", "Rosé Pine Dawn": "<PERSON><PERSON><PERSON>", "RTL": "RTL", "Run": "Futtatás", "Running": "Fu<PERSON>", "Save": "Men<PERSON>s", "Save & Create": "Mentés és létrehozás", "Save & Update": "Mentés és frissítés", "Save As Copy": "Men<PERSON>s <PERSON>", "Save Tag": "<PERSON><PERSON><PERSON><PERSON>", "Saved": "Mentve", "Saving chat logs directly to your browser's storage is no longer supported. Please take a moment to download and delete your chat logs by clicking the button below. Don't worry, you can easily re-import your chat logs to the backend through": "A csevegési naplók közvetlen mentése a böngésző tárolójába már nem támogatott. K<PERSON><PERSON><PERSON><PERSON><PERSON>, sz<PERSON><PERSON> egy percet a csevegési naplók letöltésére és törlésére az alábbi gomb megnyom<PERSON>ával. Ne aggódjon, könnyen újra importálhatja a csevegési naplókat a backend-be", "Scroll On Branch Change": "", "Search": "Keresés", "Search a model": "<PERSON><PERSON>", "Search Base": "Keresési alap", "Search Chats": "Beszélgetések keresése", "Search Collection": "Gyűjtemény k<PERSON>sése", "Search Filters": "Keresési szűrők", "search for tags": "címkék keresése", "Search Functions": "Funkciók keresése", "Search Knowledge": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> k<PERSON>", "Search Models": "Modellek keresése", "Search Notes": "", "Search options": "Keresési opciók", "Search Prompts": "Promptok keresése", "Search Result Count": "Keresési találatok száma", "Search the internet": "Keresés az interneten", "Search Tools": "Eszközök keresése", "SearchApi API Key": "SearchApi API kulcs", "SearchApi Engine": "SearchApi motor", "Searched {{count}} sites": "{{count}} old<PERSON> keres<PERSON>e me<PERSON>", "Searching \"{{searchQuery}}\"": "Keresés: \"{{searchQuery}}\"", "Searching Knowledge for \"{{searchQuery}}\"": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> k<PERSON>: \"{{searchQuery}}\"", "Searching the web...": "", "Searxng Query URL": "Searxng lekérdezési URL", "See readme.md for instructions": "Lásd a readme.md fájlt az útmutatásért", "See what's new": "Újdonságok megtekintése", "Seed": "Seed", "Select a base model": "V<PERSON><PERSON><PERSON> egy al<PERSON>", "Select a engine": "Válasszon egy motort", "Select a function": "Válasszon egy funkciót", "Select a group": "Válasszon egy c<PERSON>ot", "Select a model": "Válasszon egy modellt", "Select a pipeline": "Válasszon egy foly<PERSON>t", "Select a pipeline url": "Válasszon egy folyamat URL-t", "Select a tool": "Válasszon egy eszközt", "Select an auth method": "Válasszon egy hitelesítési módszert", "Select an Ollama instance": "V<PERSON>lass<PERSON> egy Olla<PERSON>", "Select Engine": "Motor kiválasztása", "Select Knowledge": "Tud<PERSON><PERSON><PERSON><PERSON><PERSON> k<PERSON>ztás<PERSON>", "Select only one model to call": "Csak egy modellt válasszon ki hívásra", "Selected model(s) do not support image inputs": "A kiválasztott modell(ek) nem támogatják a képbemenetet", "Semantic distance to query": "Szemantikai távolság a lekérdezéshez", "Send": "<PERSON><PERSON><PERSON><PERSON>", "Send a Message": "Üzenet küldése", "Send message": "Üzenet küldése", "Sends `stream_options: { include_usage: true }` in the request.\nSupported providers will return token usage information in the response when set.": "A kérésben elküldi a `stream_options: { include_usage: true }` opciót.\nA támogatott szolgáltatók token használati információt küldenek vissza a válaszban, ha be van állítva.", "September": "Szeptember", "SerpApi API Key": "SerpApi API kulcs", "SerpApi Engine": "SerpApi motor", "Serper API Key": "Serper API kulcs", "Serply API Key": "Serply API kulcs", "Serpstack API Key": "Serpstack API kulcs", "Server connection verified": "Szerverkapcsolat ellenőrizve", "Set as default": "Beállítás alapértelmezettként", "Set CFG Scale": "CFG skála beállítása", "Set Default Model": "Alapértelmezett modell be<PERSON>", "Set embedding model": "Beágyazási modell beállí<PERSON>a", "Set embedding model (e.g. {{model}})": "Beágyazási modell be<PERSON>ll<PERSON> (pl. {{model}})", "Set Image Size": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Set reranking model (e.g. {{model}})": "Újrarangsoroló modell be<PERSON> (pl. {{model}})", "Set Sampler": "Mintavételező beállítása", "Set Scheduler": "Ütemező beállítása", "Set Steps": "Lépések beállítása", "Set the number of layers, which will be off-loaded to GPU. Increasing this value can significantly improve performance for models that are optimized for GPU acceleration but may also consume more power and GPU resources.": "Állítsd be a GPU-ra áthelyezett rétegek számát. Ezen érték növelése jelentősen javíthatja a GPU-ra optimalizált modellek teljesítmé<PERSON>t, de több energiát és GPU erőforrást is fogyaszthat.", "Set the number of worker threads used for computation. This option controls how many threads are used to process incoming requests concurrently. Increasing this value can improve performance under high concurrency workloads but may also consume more CPU resources.": "Állítsd be a számítási feladatokhoz használt munkaszálak számát. Ez az opció <PERSON>, hány szál dolgozik párhuzamosan a bejövő kérések feldolgozásán. Az érték növelése javíthatja a teljesítményt nagy párhuzamosságú munkaterhelés esetén, de több CPU-erőforrást is fogyaszthat.", "Set Voice": "<PERSON> <PERSON><PERSON><PERSON><PERSON>", "Set whisper model": "Whisper <PERSON><PERSON>", "Sets a flat bias against tokens that have appeared at least once. A higher value (e.g., 1.5) will penalize repetitions more strongly, while a lower value (e.g., 0.9) will be more lenient. At 0, it is disabled.": "Egységes elfogultságot állít be az egyszer már meg<PERSON> ellen. <PERSON><PERSON><PERSON><PERSON> (pl. 1,5) er<PERSON><PERSON><PERSON><PERSON> bünteti az ismétléseket, al<PERSON><PERSON><PERSON><PERSON> (pl. 0,9) engedékenyebb. 0-nál kikapcsolva.", "Sets a scaling bias against tokens to penalize repetitions, based on how many times they have appeared. A higher value (e.g., 1.5) will penalize repetitions more strongly, while a lower value (e.g., 0.9) will be more lenient. At 0, it is disabled.": "Skálázott elfogultságot állít be az ismétlések büntetésére a tokenek megjelenési száma alapján. <PERSON><PERSON><PERSON><PERSON> (pl. 1,5) er<PERSON><PERSON><PERSON><PERSON> bünte<PERSON> az is<PERSON><PERSON>et, al<PERSON><PERSON>yabb <PERSON>k (pl. 0,9) engedékenyebb. 0-nál kikapcsolva.", "Sets how far back for the model to look back to prevent repetition.": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, hogy a modell mennyire nézzen vissza az ismétlések elkerülése érdekében.", "Sets the random number seed to use for generation. Setting this to a specific number will make the model generate the same text for the same prompt.": "Beállítja a generáláshoz használt véletlenszám seed-et. Egy adott szám megadása esetén a modell ugyanazt a szöveget generálja ugyanarra a prompt-ra.", "Sets the size of the context window used to generate the next token.": "Beállítja a következő token generálásához használt kontextusablak méretét.", "Sets the stop sequences to use. When this pattern is encountered, the LLM will stop generating text and return. Multiple stop patterns may be set by specifying multiple separate stop parameters in a modelfile.": "Beállítja a használandó leállítási szekvenciákat. Ha ezt a mintát é<PERSON>leli, az LLM leállítja a szöveg generálását és visszatér. Több leállítási minta is megadható különálló stop paraméterek megadásával egy modellfájlban.", "Settings": "Beállítások", "Settings saved successfully!": "Beállítások sikeresen mentve!", "Share": "Megosztás", "Share Chat": "Beszélgetés megosztása", "Share to Open WebUI Community": "Megosztás az OpenWebUI közösséggel", "Sharing Permissions": "Megoszt<PERSON><PERSON> en<PERSON>", "Shortcuts with an asterisk (*) are situational and only active under specific conditions.": "", "Show": "Mutat", "Show \"What's New\" modal on login": "\"Mi újság\" modal megjelenítése bejelentkezéskor", "Show Admin Details in Account Pending Overlay": "Admin részletek megjelenítése a függő fiók átfedésben", "Show All": "", "Show image preview": "", "Show Less": "", "Show Model": "<PERSON><PERSON>", "Show shortcuts": "Gyorsbillentyűk megjelenítése", "Show your support!": "Mutassa meg tá<PERSON>át!", "Showcased creativity": "<PERSON>rea<PERSON><PERSON><PERSON><PERSON> be<PERSON>", "Sign in": "Bejelentkezés", "Sign in to {{WEBUI_NAME}}": "Bejelentkezés ide: {{WEBUI_NAME}}", "Sign in to {{WEBUI_NAME}} with LDAP": "Bejelentkezés ide: {{WEBUI_NAME}} LDAP-val", "Sign Out": "Kijelentkezés", "Sign up": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Sign up to {{WEBUI_NAME}}": "Regisztráció ide: {{WEBUI_NAME}}", "Significantly improves accuracy by using an LLM to enhance tables, forms, inline math, and layout detection. Will increase latency. Defaults to True.": "", "Signing in to {{WEBUI_NAME}}": "Bejelentkezés ide: {{WEBUI_NAME}}", "sk-1234": "sk-1234", "Skip Cache": "", "Skip the cache and re-run the inference. Defaults to False.": "", "Sougou Search API sID": "", "Sougou Search API SK": "", "Source": "<PERSON><PERSON><PERSON>", "Speech Playback Speed": "<PERSON><PERSON><PERSON><PERSON>", "Speech recognition error: {{error}}": "Beszédfelismerési hiba: {{error}}", "Speech-to-Text": "", "Speech-to-Text Engine": "Beszéd-szöveg motor", "Stop": "Le<PERSON><PERSON><PERSON><PERSON><PERSON>", "Stop Generating": "", "Stop Sequence": "Leállítási szekvencia", "Stream Chat Response": "Chat v<PERSON>lasz streamel<PERSON>e", "Strikethrough": "", "Strip Existing OCR": "", "Strip existing OCR text from the PDF and re-run OCR. Ignored if Force OCR is enabled. Defaults to False.": "", "STT Model": "STT modell", "STT Settings": "STT beállítások", "Stylized PDF Export": "", "Subtitle (e.g. about the Roman Empire)": "Alcím (pl. a Római Birodalomról)", "Success": "Siker", "Successfully updated.": "Sikeresen frissítve.", "Suggested": "Javasolt", "Support": "Támogatás", "Support this plugin:": "Támogassa ezt a bővítményt:", "Supported MIME Types": "", "Sync directory": "K<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "System": "Rendszer", "System Instructions": "Rendszer utasítások", "System Prompt": "<PERSON><PERSON><PERSON> prompt", "Tags": "Címkék", "Tags Generation": "Címke <PERSON>", "Tags Generation Prompt": "<PERSON><PERSON><PERSON><PERSON> prompt", "Tail free sampling is used to reduce the impact of less probable tokens from the output. A higher value (e.g., 2.0) will reduce the impact more, while a value of 1.0 disables this setting.": "A farok nélküli mintavétel csökkenti a kevésbé valószínű tokenek hatását a kimeneten. Magasabb érték (pl. 2,0) jobban csökkenti a hat<PERSON><PERSON>, míg az 1,0 érték kikapcsolja ezt a beállítást.", "Talk to model": "<PERSON><PERSON><PERSON><PERSON><PERSON> a <PERSON>lel", "Tap to interrupt": "Koppintson a megszakításhoz", "Task List": "", "Task Model": "", "Tasks": "Feladatok", "Tavily API Key": "Tavily API kulcs", "Tavily Extract Depth": "", "Tell us more:": "Mondjon többet:", "Temperature": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Temporary Chat": "Ideiglenes cseve<PERSON>s", "Text Splitter": "Szöveg felosztó", "Text-to-Speech": "", "Text-to-Speech Engine": "Szöveg-beszéd motor", "Thanks for your feedback!": "Köszönjük a visszajelzést!", "The Application Account DN you bind with for search": "Az alkalmazás fiók DN, amellyel kereséshez k<PERSON>z", "The base to search for users": "A felhasználók keresésének alapja", "The batch size determines how many text requests are processed together at once. A higher batch size can increase the performance and speed of the model, but it also requires more memory.": "A köteg méret me<PERSON>, hány szöveges kérést dolgoz fel egyszerre. Magasabb köteg méret növelheti a modell teljesítményét és sebességét, de több memóri<PERSON>t is igényel.", "The developers behind this plugin are passionate volunteers from the community. If you find this plugin helpful, please consider contributing to its development.": "A bővítmény fejlesztői lelkes önkéntesek a közösségből. Ha hasznosnak találja ezt a bővítményt, k<PERSON><PERSON><PERSON><PERSON><PERSON>, fontolja meg a fejlesztéséhez való hozzájárulást.", "The evaluation leaderboard is based on the Elo rating system and is updated in real-time.": "Az értékelési ranglista az Elo értékelési rendszeren alapul és valós időben frissül.", "The format to return a response in. Format can be json or a JSON schema.": "", "The height in pixels to compress images to. Leave empty for no compression.": "", "The language of the input audio. Supplying the input language in ISO-639-1 (e.g. en) format will improve accuracy and latency. Leave blank to automatically detect the language.": "", "The LDAP attribute that maps to the mail that users use to sign in.": "Az LDAP attribútum, amely a felhasználók bejelentkezéshez használt emailjéhez ka<PERSON>olódik.", "The LDAP attribute that maps to the username that users use to sign in.": "Az LDAP attribútum, amely a felhasználók bejelentkezéshez használt felhasználónevéhez kapcsolódik.", "The leaderboard is currently in beta, and we may adjust the rating calculations as we refine the algorithm.": "A ranglista jelenleg béta verz<PERSON> van, és az algoritmus finomítása során módosíthatjuk az értékelési számításokat.", "The maximum file size in MB. If the file size exceeds this limit, the file will not be uploaded.": "A maximális fájlméret MB-ban. Ha a fájlméret meghaladja ezt a limitet, a fájl nem lesz feltöltve.", "The maximum number of files that can be used at once in chat. If the number of files exceeds this limit, the files will not be uploaded.": "A csevegésben egyszerre használható fájlok maximális száma. Ha a fájlok száma meghaladja ezt a limitet, a fájlok nem lesznek feltöltve.", "The output format for the text. Can be 'json', 'markdown', or 'html'. Defaults to 'markdown'.": "", "The score should be a value between 0.0 (0%) and 1.0 (100%).": "A pontszámnak 0,0 (0%) és 1,0 (100%) közötti értéknek kell lennie.", "The temperature of the model. Increasing the temperature will make the model answer more creatively.": "A modell hőmérséklete. A hőmérséklet növelése kreatívabb válaszokat eredményez a modelltől.", "The width in pixels to compress images to. Leave empty for no compression.": "", "Theme": "<PERSON><PERSON><PERSON>", "Thinking...": "Gondolkodik...", "This action cannot be undone. Do you wish to continue?": "Ez a mű<PERSON>et nem von<PERSON>ó v<PERSON>za. Szeretné folytatni?", "This channel was created on {{createdAt}}. This is the very beginning of the {{channelName}} channel.": "Ez a csatorna {{createdAt}} időpontban j<PERSON>tt l<PERSON>. Ez a {{channelName}} csatorna legeslegeleje.", "This chat won't appear in history and your messages will not be saved.": "Ez a csevegés nem jelenik meg az előzményekben, és az üzeneteid nem kerülnek mentésre.", "This ensures that your valuable conversations are securely saved to your backend database. Thank you!": "Ez biztosítja, hogy értékes beszélgetései biztonságosan mentésre kerüljenek a backend adatbázisban. Köszönjük!", "This feature is experimental and may be modified or discontinued without notice.": "", "This is an experimental feature, it may not function as expected and is subject to change at any time.": "<PERSON>z egy kís<PERSON><PERSON><PERSON>, le<PERSON><PERSON>, hogy nem a várt módon működik és bármikor változhat.", "This model is not publicly available. Please select another model.": "", "This option controls how long the model will stay loaded into memory following the request (default: 5m)": "", "This option controls how many tokens are preserved when refreshing the context. For example, if set to 2, the last 2 tokens of the conversation context will be retained. Preserving context can help maintain the continuity of a conversation, but it may reduce the ability to respond to new topics.": "<PERSON>z az opció <PERSON>, h<PERSON><PERSON> token marad meg a kontextus frissítésekor. <PERSON><PERSON><PERSON><PERSON><PERSON>, ha 2-re <PERSON>, a beszélgetés kontextusának utolsó 2 tokenje megmarad. A kontextus megőrzése segíthet a beszélgetés folytonosságának fenntart<PERSON>ban, de csökkentheti az új témákra való reagálás képességét.", "This option enables or disables the use of the reasoning feature in Ollama, which allows the model to think before generating a response. When enabled, the model can take a moment to process the conversation context and generate a more thoughtful response.": "", "This option sets the maximum number of tokens the model can generate in its response. Increasing this limit allows the model to provide longer answers, but it may also increase the likelihood of unhelpful or irrelevant content being generated.": "Ez az opció beállítja a modell által generálható tokenek maximális számát a válaszban. Ezen limit növelése hosszabb válaszokat tesz lehetővé, de növelheti a nem hasznos vagy irreleváns tartalom generálásának valószínűségét.", "This option will delete all existing files in the collection and replace them with newly uploaded files.": "Ez az opció törli az összes meglévő fájlt a gyűjteményben és lecseréli őket az újonnan feltöltött fájlokkal.", "This response was generated by \"{{model}}\"": "Ezt a választ a \"{{model}}\" generálta", "This will delete": "Ez törölni fogja", "This will delete <strong>{{NAME}}</strong> and <strong>all its contents</strong>.": "<PERSON>z törölni fogja a <strong>{{NAME}}</strong>-t <PERSON>s <strong>minden tarta<PERSON></strong>.", "This will delete all models including custom models": "Ez törölni fogja az összes modellt, beleértve az egyéni modelleket is", "This will delete all models including custom models and cannot be undone.": "Ez törölni fogja az összes modellt, beleértve az egyéni modelleket is, és nem vonható v<PERSON>.", "This will reset the knowledge base and sync all files. Do you wish to continue?": "Ez visszaállítja a tudásbázist és szinkronizálja az összes fájlt. Szeretné folytatni?", "Thorough explanation": "Alapos magyarázat", "Thought for {{DURATION}}": "Gondolkodott {{DURATION}} ideig", "Thought for {{DURATION}} seconds": "Gondolkodott {{DURATION}} másodpercig", "Tika": "<PERSON><PERSON>", "Tika Server URL required.": "Tika szerver URL szükséges.", "Tiktoken": "Tiktoken", "Tip: Update multiple variable slots consecutively by pressing the tab key in the chat input after each replacement.": "Tipp: Frissítsen több változó helyet egymás után a tab billentyű megnyomásával a csevegő bevitelben minden helyettesítés után.", "Title": "Cím", "Title (e.g. Tell me a fun fact)": "Cím (pl. Mon<PERSON>j egy érdekes tényt)", "Title Auto-Generation": "Cím automatikus generál<PERSON>", "Title cannot be an empty string.": "A cím nem lehet üres karakterlánc.", "Title Generation": "<PERSON><PERSON><PERSON>", "Title Generation Prompt": "<PERSON><PERSON><PERSON> prompt", "TLS": "TLS", "To access the available model names for downloading,": "A letölthető modellek nevének eléréséhez,", "To access the GGUF models available for downloading,": "A letölthető GGUF modellek eléréséhez,", "To access the WebUI, please reach out to the administrator. Admins can manage user statuses from the Admin Panel.": "A WebUI eléréséhez ké<PERSON>, forduljon az adminisztrátorhoz. Az adminisztrátorok az Admin Panelen keresztül kezelhetik a felhasználói státuszokat.", "To attach knowledge base here, add them to the \"Knowledge\" workspace first.": "A tudásbázis csatolásához először adja hozzá őket a \"Knowledge\" munkaterülethez.", "To learn more about available endpoints, visit our documentation.": "Az elérhető végpontokról további információért látogassa meg dokumentációnkat.", "To protect your privacy, only ratings, model IDs, tags, and metadata are shared from your feedback—your chat logs remain private and are not included.": "Adatai védelme érdekében a visszajelzésből csak az értékelések, modell <PERSON>, címkék és metaadatok kerülnek megosztásra - a csevegési előzményei privátak maradnak és nem kerülnek megosztásra.", "To select actions here, add them to the \"Functions\" workspace first.": "A műveletek kiválasztásához először adja hozzá őket a \"Functions\" munkaterülethez.", "To select filters here, add them to the \"Functions\" workspace first.": "A szűrők kiválasztásához először adja hozzá őket a \"Functions\" munkaterülethez.", "To select toolkits here, add them to the \"Tools\" workspace first.": "Az eszközkészletek kiválasztásához először adja hozzá őket a \"Tools\" munkaterülethez.", "Toast notifications for new updates": "Felugró értesítések az új frissítésekről", "Today": "Ma", "Toggle search": "", "Toggle settings": "Beállítások be/ki", "Toggle sidebar": "Oldalsáv be/ki", "Toggle whether current connection is active.": "", "Token": "Token", "Too verbose": "<PERSON><PERSON>", "Tool created successfully": "Eszköz <PERSON> létrehozva", "Tool deleted successfully": "Eszköz si<PERSON>esen törölve", "Tool Description": "Eszköz <PERSON>", "Tool ID": "Eszköz azonosító", "Tool imported successfully": "Eszköz <PERSON>", "Tool Name": "Eszköz neve", "Tool Servers": "Eszközszerverek", "Tool updated successfully": "Eszköz si<PERSON>esen frissítve", "Tools": "Eszközök", "Tools Access": "Eszközök hozzáférése", "Tools are a function calling system with arbitrary code execution": "Az eszközök olyan függvényhívó rendszert alkotnak, amely tetszőleges kód végrehajtását teszi lehetővé", "Tools Function Calling Prompt": "Eszközök függvényhívási promptja", "Tools have a function calling system that allows arbitrary code execution.": "Az eszközök olyan függvényhívó rendszerrel rendelkeznek, amely lehetővé teszi tetszőleges kód végrehajtását.", "Tools Public Sharing": "Eszközök nyilvános megosztása", "Top K": "Top K", "Top K Reranker": "Top K újrarangsoroló", "Transformers": "Transformerek", "Trouble accessing Ollama?": "Problé<PERSON><PERSON><PERSON> van az Ollama elérésével?", "Trust Proxy Environment": "Proxy környezet megbízhatósága", "TTS Model": "TTS modell", "TTS Settings": "TTS beállítások", "TTS Voice": "TTS hang", "Type": "<PERSON><PERSON><PERSON>", "Type Hugging Face Resolve (Download) URL": "Adja meg a Hugging Face Resolve (Letöltési) URL-t", "Uh-oh! There was an issue with the response.": "Jaj! Probléma adódott a válasszal.", "UI": "Felhasználói felület", "Unarchive All": "Minden visszaállítása", "Unarchive All Archived Chats": "Minden archivált csevegés v<PERSON>llítása", "Unarchive Chat": "Csevegés v<PERSON>llítása", "Underline": "", "Unloads {{FROM_NOW}}": "", "Unlock mysteries": "Titkok feloldása", "Unpin": "Rögzí<PERSON>s felo<PERSON>", "Unravel secrets": "Titkok megfejtése", "Untagged": "Címkézetlen", "Untitled": "", "Update": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Update and Copy Link": "Frissítés és link másolása", "Update for the latest features and improvements.": "Frissítsen a legújabb funkciókért és fejlesztésekért.", "Update password": "<PERSON><PERSON><PERSON><PERSON> f<PERSON>", "Updated": "Frissítve", "Updated at": "Frissítve ekkor", "Updated At": "Frissítve ekkor", "Upgrade to a licensed plan for enhanced capabilities, including custom theming and branding, and dedicated support.": "Frissítsen licencelt csomagra a bővített képességekért, beleértve az egyéni témázást és márkázást, valamint dedikált támogatást.", "Upload": "Feltöltés", "Upload a GGUF model": "GGUF modell feltöltése", "Upload Audio": "", "Upload directory": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Upload files": "Fájlok feltöltése", "Upload Files": "Fájlok feltöltése", "Upload Pipeline": "<PERSON><PERSON><PERSON><PERSON>", "Upload Progress": "Feltöltési folyamat", "URL": "URL", "URL Mode": "URL mód", "Usage": "", "Use '#' in the prompt input to load and include your knowledge.": "Haszná<PERSON>ja a '#' karaktert a prompt bevitelénél a tudásbázis betöltéséhez és felhasználásához.", "Use Gravatar": "Gravat<PERSON>", "Use groups to group your users and assign permissions.": "Használj csoportokat a felhasználók csoportosításához és engedélyek hozzárendeléséhez.", "Use Initials": "Monogram használata", "Use LLM": "", "Use no proxy to fetch page contents.": "", "Use proxy designated by http_proxy and https_proxy environment variables to fetch page contents.": "", "user": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "User": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "User location successfully retrieved.": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> he<PERSON>e si<PERSON>n le<PERSON>.", "User menu": "", "User Webhooks": "Felhasználói webhookok", "Username": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Users": "Felhasználók", "Using the default arena model with all models. Click the plus button to add custom models.": "Az alapértelmezett aréna modell használata az összes modellel. Kattintson a plusz gombra egyéni modellek hozzáadásához.", "Utilize": "<PERSON><PERSON><PERSON><PERSON>", "Valid time units:": "Érvényes időegységek:", "Valves": "Szelepek", "Valves updated": "Szelepek frissítve", "Valves updated successfully": "Szelepek sikeresen frissítve", "variable": "változó", "variable to have them replaced with clipboard content.": "v<PERSON><PERSON><PERSON><PERSON>, hogy a vágólap tartalmával helyettesítse őket.", "Verify Connection": "Kap<PERSON>olat ellenőrzése", "Verify SSL Certificate": "", "Version": "<PERSON><PERSON><PERSON><PERSON>", "Version {{selectedVersion}} of {{totalVersions}}": "{{selectedVersion}}. verzió a {{totalVersions}}-ból", "View Replies": "Válaszok megtekintése", "View Result from **{{NAME}}**": "<PERSON><PERSON><PERSON><PERSON> megtekintése innen: **{{NAME}}**", "Visibility": "<PERSON><PERSON><PERSON><PERSON>", "Vision": "", "Voice": "Hang", "Voice Input": "Hangbevitel", "Voice mode": "", "Warning": "Figyelmeztetés", "Warning:": "Figyelmeztetés:", "Warning: Enabling this will allow users to upload arbitrary code on the server.": "Figyelmeztetés: Ennek engedélyezése lehetővé teszi a felhasználók számára, hogy tetszőleges kódot töltsenek fel a szerverre.", "Warning: If you update or change your embedding model, you will need to re-import all documents.": "Figyelmeztetés: Ha frissíti vagy megváltoztatja a beágyazási modellt, minden dokumentumot újra kell importálnia.", "Warning: Jupyter execution enables arbitrary code execution, posing severe security risks—proceed with extreme caution.": "Figyelmeztetés: A Jupyter végrehajtás lehetővé teszi a tetszőleges kód végrehajtását, ami s<PERSON>yos biz<PERSON> kockázatot jelent – óvatosan folytassa.", "Web": "Web", "Web API": "Web API", "Web Loader Engine": "", "Web Search": "Webes kere<PERSON>", "Web Search Engine": "<PERSON>es keresőmotor", "Web Search in Chat": "Webes keresés a csevegésben", "Web Search Query Generation": "Webes keresési lekérdezés generálása", "Webhook URL": "Webhook URL", "WebUI Settings": "WebUI beállítások", "WebUI URL": "WebUI URL", "WebUI will make requests to \"{{url}}\"": "A WebUI kéréseket küld a \"{{url}}\" címre", "WebUI will make requests to \"{{url}}/api/chat\"": "A WebUI kéréseket küld a \"{{url}}/api/chat\" címre", "WebUI will make requests to \"{{url}}/chat/completions\"": "A WebUI kéréseket küld a \"{{url}}/chat/completions\" címre", "Weight of BM25 Retrieval": "", "What are you trying to achieve?": "<PERSON>t pr<PERSON><PERSON><PERSON><PERSON>z elérni?", "What are you working on?": "Min dolgo<PERSON>l?", "What's New in": "Mi újság a", "When enabled, the model will respond to each chat message in real-time, generating a response as soon as the user sends a message. This mode is useful for live chat applications, but may impact performance on slower hardware.": "Ha engedélyezve van, a modell valós időben válaszol minden csevegőüzenetre, amint a felhasználó elküldi az üzenetet. Ez a mód hasznos élő csevegőalkalmazásokhoz, de lassabb hardveren befolyásolhatja a teljesítményt.", "wherever you are": "b<PERSON><PERSON><PERSON> is vagy", "Whether to paginate the output. Each page will be separated by a horizontal rule and page number. Defaults to False.": "", "Whisper (Local)": "Whisper (helyi)", "Why?": "<PERSON><PERSON><PERSON>?", "Widescreen Mode": "Szélesvásznú mód", "Won": "Nyert", "Works together with top-k. A higher value (e.g., 0.95) will lead to more diverse text, while a lower value (e.g., 0.5) will generate more focused and conservative text.": "A top-k-val együtt működik. <PERSON><PERSON><PERSON><PERSON> (pl. 0,95) változatosabb szöveget eredmé<PERSON>ez, alacsonyabb <PERSON>k (pl. 0,5) fókuszáltabb és konzervatívabb szöveget generál.", "Workspace": "Munkaterület", "Workspace Permissions": "<PERSON><PERSON><PERSON><PERSON><PERSON> en<PERSON>é<PERSON>ek", "Write": "<PERSON><PERSON><PERSON>", "Write a prompt suggestion (e.g. Who are you?)": "<PERSON><PERSON><PERSON> egy prompt java<PERSON><PERSON>t (pl. <PERSON> vagy te?)", "Write a summary in 50 words that summarizes [topic or keyword].": "Írjon egy 50 szavas összefoglalót a [téma vagy k<PERSON>]-ról.", "Write something...": "<PERSON><PERSON><PERSON> valamit...", "Yacy Instance URL": "", "Yacy Password": "", "Yacy Username": "", "Yesterday": "Tegnap", "You": "<PERSON><PERSON>", "You are currently using a trial license. Please contact support to upgrade your license.": "Jelenleg próbaverziós licencet használ. K<PERSON><PERSON><PERSON><PERSON><PERSON>, lépjen ka<PERSON>olatba a támogatással a licenc frissítéséhez.", "You can only chat with a maximum of {{maxCount}} file(s) at a time.": "Egyszerre maximum {{maxCount}} fáj<PERSON>l tud csevegni.", "You can personalize your interactions with LLMs by adding memories through the 'Manage' button below, making them more helpful and tailored to you.": "Az LLM-ek<PERSON> való interakcióit személyre szabhatja emlékek hozzáadásával a lenti 'Kezelés' gomb seg<PERSON><PERSON>, <PERSON>gy azok még hasznosabbak és személyre szabottabbak lesznek.", "You cannot upload an empty file.": "<PERSON>em tölt<PERSON>t fel üres fájlt.", "You do not have permission to upload files.": "<PERSON><PERSON><PERSON> fájlok feltöltésére.", "You have no archived conversations.": "Nincsenek archivált <PERSON>.", "You have shared this chat": "Megosztotta ezt a beszélgetést", "You're a helpful assistant.": "<PERSON>n egy segít<PERSON>k<PERSON>z asszisztens.", "You're now logged in.": "<PERSON><PERSON><PERSON><PERSON> be<PERSON>.", "Your account status is currently pending activation.": "Fiókja jelenleg aktiválásra vár.", "Your entire contribution will go directly to the plugin developer; Open WebUI does not take any percentage. However, the chosen funding platform might have its own fees.": "A teljes hozzájárulása közvetlenül a bővítmény fejlesztőjéhez kerül; az Open WebUI nem vesz le százalékot. Azonban a választott támogatási platformnak lehetnek saját díjai.", "Youtube": "YouTube", "Youtube Language": "YouTube nyelv", "Youtube Proxy URL": "YouTube proxy URL"}