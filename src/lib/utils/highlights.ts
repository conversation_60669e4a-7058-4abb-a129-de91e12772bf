import { get } from 'svelte/store';
import { highlightedSegments, type HighlightedSegment } from '$lib/stores';
import { v4 as uuidv4 } from 'uuid';

/**
 * Add a new highlighted text segment
 */
export function addHighlight(
	text: string,
	messageId: string,
	chatId: string,
	startOffset: number,
	endOffset: number
): string {
	const id = uuidv4();
	const highlight: HighlightedSegment = {
		id,
		text,
		messageId,
		chatId,
		timestamp: Date.now(),
		startOffset,
		endOffset
	};

	highlightedSegments.update((segments) => [...segments, highlight]);
	
	// Store in localStorage for persistence
	localStorage.setItem('highlightedSegments', JSON.stringify(get(highlightedSegments)));
	
	return id;
}

/**
 * Remove a highlighted text segment by ID
 */
export function removeHighlight(highlightId: string): void {
	highlightedSegments.update((segments) => 
		segments.filter((segment) => segment.id !== highlightId)
	);
	
	// Update localStorage
	localStorage.setItem('highlightedSegments', JSON.stringify(get(highlightedSegments)));
}

/**
 * Get all highlighted segments for a specific message
 */
export function getHighlightsForMessage(messageId: string): HighlightedSegment[] {
	return get(highlightedSegments).filter((segment) => segment.messageId === messageId);
}

/**
 * Get all highlighted segments for a specific chat
 */
export function getHighlightsForChat(chatId: string): HighlightedSegment[] {
	return get(highlightedSegments).filter((segment) => segment.chatId === chatId);
}

/**
 * Clear all highlighted segments
 */
export function clearAllHighlights(): void {
	highlightedSegments.set([]);
	localStorage.removeItem('highlightedSegments');
}

/**
 * Clear highlights for a specific chat
 */
export function clearHighlightsForChat(chatId: string): void {
	highlightedSegments.update((segments) => 
		segments.filter((segment) => segment.chatId !== chatId)
	);
	
	// Update localStorage
	localStorage.setItem('highlightedSegments', JSON.stringify(get(highlightedSegments)));
}

/**
 * Load highlighted segments from localStorage
 */
export function loadHighlightsFromStorage(): void {
	try {
		const stored = localStorage.getItem('highlightedSegments');
		if (stored) {
			const segments: HighlightedSegment[] = JSON.parse(stored);
			highlightedSegments.set(segments);
		}
	} catch (error) {
		console.error('Failed to load highlights from storage:', error);
		highlightedSegments.set([]);
	}
}

/**
 * Get all highlighted text as a single string
 */
export function getAllHighlightedText(): string {
	const segments = get(highlightedSegments);
	return segments
		.sort((a, b) => a.timestamp - b.timestamp)
		.map((segment) => segment.text)
		.join('\n\n');
}

/**
 * Copy all highlighted text to clipboard
 */
export async function copyAllHighlights(): Promise<boolean> {
	try {
		const text = getAllHighlightedText();
		if (text.trim()) {
			await navigator.clipboard.writeText(text);
			return true;
		}
		return false;
	} catch (error) {
		console.error('Failed to copy highlights to clipboard:', error);
		return false;
	}
}

/**
 * Check if a text range is already highlighted
 */
export function isRangeHighlighted(
	messageId: string,
	startOffset: number,
	endOffset: number
): HighlightedSegment | null {
	const segments = getHighlightsForMessage(messageId);
	return segments.find((segment) => 
		segment.startOffset === startOffset && segment.endOffset === endOffset
	) || null;
}
