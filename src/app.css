@reference "./tailwind.css";

@font-face {
	font-family: 'Inter';
	src: url('/assets/fonts/Inter-Variable.ttf');
	font-display: swap;
}

@font-face {
	font-family: 'Archivo';
	src: url('/assets/fonts/Archivo-Variable.ttf');
	font-display: swap;
}

@font-face {
	font-family: 'Mona Sans';
	src: url('/assets/fonts/Mona-Sans.woff2');
	font-display: swap;
}

@font-face {
	font-family: 'InstrumentSerif';
	src: url('/assets/fonts/InstrumentSerif-Regular.ttf');
	font-display: swap;
}

@font-face {
	font-family: 'Vazirmatn';
	src: url('/assets/fonts/Vazirmatn-Variable.ttf');
	font-display: swap;
}

html {
	word-break: break-word;
}

code {
	/* white-space-collapse: preserve !important; */
	overflow-x: auto;
	width: auto;
}

.font-secondary {
	font-family: 'InstrumentSerif', sans-serif;
}

.marked a {
	@apply underline;
}

math {
	margin-top: 1rem;
}

.hljs {
	@apply rounded-lg;
}

input::placeholder {
	direction: auto;
}

textarea::placeholder {
	direction: auto;
}

.input-prose {
	@apply prose dark:prose-invert prose-headings:font-semibold prose-hr:my-4 prose-hr:border-gray-100 prose-hr:dark:border-gray-800 prose-p:my-1 prose-img:my-1 prose-headings:my-2 prose-pre:my-0 prose-table:my-1 prose-blockquote:my-0 prose-ul:my-1 prose-ol:my-1 prose-li:my-0.5 whitespace-pre-line;
}

.input-prose-sm {
	@apply prose dark:prose-invert prose-headings:font-medium prose-h1:text-2xl prose-h2:text-xl prose-h3:text-lg prose-hr:my-4 prose-hr:border-gray-100 prose-hr:dark:border-gray-800 prose-p:my-1 prose-img:my-1 prose-headings:my-2 prose-pre:my-0 prose-table:my-1 prose-blockquote:my-0 prose-ul:my-1 prose-ol:my-1 prose-li:my-1 whitespace-pre-line text-sm;
}

.markdown-prose {
	@apply prose dark:prose-invert prose-blockquote:border-s-gray-100 prose-blockquote:dark:border-gray-800 prose-blockquote:border-s-2 prose-blockquote:not-italic prose-blockquote:font-normal  prose-headings:font-semibold prose-hr:my-4 prose-hr:border-gray-100 prose-hr:dark:border-gray-800 prose-p:my-0 prose-img:my-1 prose-headings:my-1 prose-pre:my-0 prose-table:my-0 prose-blockquote:my-0 prose-ul:-my-0 prose-ol:-my-0 prose-li:-my-0 whitespace-pre-line;
}

.markdown-prose-sm {
	@apply text-sm prose dark:prose-invert prose-blockquote:border-s-gray-100 prose-blockquote:dark:border-gray-800 prose-blockquote:border-s-2 prose-blockquote:not-italic prose-blockquote:font-normal  prose-headings:font-semibold prose-hr:my-2  prose-hr:border-gray-100 prose-hr:dark:border-gray-800 prose-p:my-0 prose-img:my-1 prose-headings:my-1 prose-pre:my-0 prose-table:my-0 prose-blockquote:my-0 prose-ul:-my-0 prose-ol:-my-0 prose-li:-my-0 whitespace-pre-line;
}

.markdown-prose-xs {
	@apply text-xs prose dark:prose-invert prose-blockquote:border-s-gray-100 prose-blockquote:dark:border-gray-800 prose-blockquote:border-s-2 prose-blockquote:not-italic prose-blockquote:font-normal  prose-headings:font-semibold prose-hr:my-0.5  prose-hr:border-gray-100 prose-hr:dark:border-gray-800 prose-p:my-0 prose-img:my-1 prose-headings:my-1 prose-pre:my-0 prose-table:my-0 prose-blockquote:my-0 prose-ul:-my-0 prose-ol:-my-0 prose-li:-my-0 whitespace-pre-line;
}

.markdown a {
	@apply underline;
}

.font-primary {
	font-family: 'Archivo', 'Vazirmatn', sans-serif;
}

.drag-region {
	-webkit-app-region: drag;
}

.drag-region a,
.drag-region button {
	-webkit-app-region: no-drag;
}

.no-drag-region {
	-webkit-app-region: no-drag;
}

li p {
	display: inline;
}

::-webkit-scrollbar-thumb {
	--tw-border-opacity: 1;
	background-color: rgba(215, 215, 215, 0.8);
	border-color: rgba(255, 255, 255, var(--tw-border-opacity));
	border-radius: 9999px;
	border-width: 1px;
}

/* Dark theme scrollbar styles */
.dark ::-webkit-scrollbar-thumb {
	background-color: rgba(67, 67, 67, 0.8); /* Darker color for dark theme */
	border-color: rgba(0, 0, 0, var(--tw-border-opacity));
}

::-webkit-scrollbar {
	height: 0.6rem;
	width: 0.4rem;
}

::-webkit-scrollbar-track {
	background-color: transparent;
	border-radius: 9999px;
}

select {
	background-image: url("data:image/svg+xml;charset=utf-8,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3E%3Cpath stroke='%236B7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3E%3C/svg%3E");
	background-position: right 0rem center;
	background-repeat: no-repeat;
	background-size: 1.5em 1.5em;
	-webkit-print-color-adjust: exact;
	print-color-adjust: exact;
	/* padding-right: 2.5rem; */
	/* for Firefox */
	-moz-appearance: none;
	/* for Chrome */
	-webkit-appearance: none;
}

@keyframes shimmer {
	0% {
		background-position: 200% 0;
	}
	100% {
		background-position: -200% 0;
	}
}

.shimmer {
	background: linear-gradient(90deg, #9a9b9e 25%, #2a2929 50%, #9a9b9e 75%);
	background-size: 200% 100%;
	background-clip: text;
	-webkit-background-clip: text;
	-webkit-text-fill-color: transparent;
	animation: shimmer 4s linear infinite;
	color: #818286; /* Fallback color */
}

:global(.dark) .shimmer {
	background: linear-gradient(90deg, #818286 25%, #eae5e5 50%, #818286 75%);
	background-size: 200% 100%;
	background-clip: text;
	-webkit-background-clip: text;
	-webkit-text-fill-color: transparent;
	animation: shimmer 4s linear infinite;
	color: #a1a3a7; /* Darker fallback color for dark mode */
}

@keyframes smoothFadeIn {
	0% {
		opacity: 0;
		transform: translateY(-10px);
	}
	100% {
		opacity: 1;
		transform: translateY(0);
	}
}

.status-description {
	animation: smoothFadeIn 0.2s forwards;
}

.katex-mathml {
	display: none;
}

.scrollbar-hidden:active::-webkit-scrollbar-thumb,
.scrollbar-hidden:focus::-webkit-scrollbar-thumb,
.scrollbar-hidden:hover::-webkit-scrollbar-thumb {
	visibility: visible;
}
.scrollbar-hidden::-webkit-scrollbar-thumb {
	visibility: hidden;
}

.scrollbar-hidden::-webkit-scrollbar-corner {
	display: none;
}

.scrollbar-none::-webkit-scrollbar {
	display: none; /* for Chrome, Safari and Opera */
}

.scrollbar-none::-webkit-scrollbar-corner {
	display: none;
}

.scrollbar-none {
	-ms-overflow-style: none; /* IE and Edge */
	scrollbar-width: none; /* Firefox */
}

input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button {
	/* display: none; <- Crashes Chrome on hover */
	-webkit-appearance: none;
	margin: 0; /* <-- Apparently some margin are still there even though it's hidden */
}

input[type='number'] {
	-moz-appearance: textfield; /* Firefox */
}

.katex-display {
	@apply overflow-y-hidden overflow-x-auto max-w-full;
}

.katex-display::-webkit-scrollbar {
	height: 0.4rem;
	width: 0.4rem;
}

.katex-display:active::-webkit-scrollbar-thumb,
.katex-display:focus::-webkit-scrollbar-thumb,
.katex-display:hover::-webkit-scrollbar-thumb {
	visibility: visible;
}
.katex-display::-webkit-scrollbar-thumb {
	visibility: hidden;
}

.katex-display::-webkit-scrollbar-corner {
	display: none;
}

.cm-editor {
	height: 100%;
	width: 100%;
}

.cm-scroller:active::-webkit-scrollbar-thumb,
.cm-scroller:focus::-webkit-scrollbar-thumb,
.cm-scroller:hover::-webkit-scrollbar-thumb {
	visibility: visible;
}

.cm-scroller::-webkit-scrollbar-thumb {
	visibility: hidden;
}

.cm-scroller::-webkit-scrollbar-corner {
	display: none;
}

.cm-editor.cm-focused {
	outline: none;
}

.tippy-box[data-theme~='dark'] {
	@apply rounded-lg bg-gray-950 text-xs border border-gray-900 shadow-xl;
}

.password {
	-webkit-text-security: disc;
}

.codespan {
	color: #eb5757;
	border-width: 0px;
	padding: 3px 8px;
	font-size: 0.8em;
	font-weight: 600;
	@apply rounded-md dark:bg-gray-800 bg-gray-100 mx-0.5;
}

.svelte-flow {
	background-color: transparent !important;
}

.svelte-flow__edge > path {
	stroke-width: 0.5;
}

.svelte-flow__edge.animated > path {
	stroke-width: 2;
	@apply stroke-gray-600 dark:stroke-gray-500;
}

.bg-gray-950-90 {
	background-color: rgba(var(--color-gray-950, #0d0d0d), 0.9);
}

/* Highlighted text styles */
.highlighted-text {
	background-color: rgba(255, 235, 59, 0.3); /* Light yellow background */
	border-radius: 2px;
	padding: 1px 2px;
	position: relative;
	cursor: pointer;
	transition: background-color 0.2s ease;
}

.highlighted-text:hover {
	background-color: rgba(255, 235, 59, 0.5);
}

.dark .highlighted-text {
	background-color: rgba(255, 193, 7, 0.2); /* Darker yellow for dark mode */
}

.dark .highlighted-text:hover {
	background-color: rgba(255, 193, 7, 0.3);
}

/* Highlight removal button */
.highlight-remove-btn {
	position: absolute;
	top: -8px;
	right: -8px;
	width: 16px;
	height: 16px;
	background-color: #ef4444;
	color: white;
	border: none;
	border-radius: 50%;
	font-size: 10px;
	line-height: 1;
	cursor: pointer;
	display: none;
	align-items: center;
	justify-content: center;
	z-index: 10;
}

.highlighted-text:hover .highlight-remove-btn {
	display: flex;
}

.ProseMirror {
	@apply h-full min-h-fit max-h-full whitespace-pre-wrap;
}

.ProseMirror:focus {
	outline: none;
}

.ProseMirror p.is-editor-empty:first-child::before {
	content: attr(data-placeholder);
	float: left;
	/* Below color is from tailwind, and has the proper contrast
	text-gray-600 from: https://tailwindcss.com/docs/color */
	color: #676767;
	pointer-events: none;

	@apply line-clamp-1 absolute;
}

.tiptap ul[data-type='taskList'] {
	list-style: none;
	margin-left: 0;
	padding: 0;

	li {
		align-items: start;
		display: flex;

		> label {
			flex: 0 0 auto;
			margin-right: 0.5rem;
			margin-top: 0.2rem;
			user-select: none;
			display: flex;
		}

		> div {
			flex: 1 1 auto;

			align-items: center;
		}
	}

	/* checked data-checked="true" */

	li[data-checked='true'] {
		> div {
			opacity: 0.5;
			text-decoration: line-through;
		}
	}

	input[type='checkbox'] {
		cursor: pointer;
	}

	ul[data-type='taskList'] {
		margin: 0;
	}

	/* Reset nested regular ul elements to default styling */
	ul:not([data-type='taskList']) {
		list-style: disc;
		padding-left: 1rem;

		li {
			align-items: initial;
			display: list-item;

			label {
				flex: initial;
				margin-right: initial;
				margin-top: initial;
				user-select: initial;
				display: initial;
			}

			div {
				flex: initial;
				align-items: initial;
			}
		}
	}
}

.input-prose .tiptap ul[data-type='taskList'] {
	list-style: none;
	margin-left: 0;
	padding: 0;

	li {
		align-items: start;
		display: flex;

		> label {
			flex: 0 0 auto;
			margin-right: 0.5rem;
			margin-top: 0.4rem;
			user-select: none;
			display: flex;
		}

		> div {
			flex: 1 1 auto;

			align-items: center;
		}
	}

	/* checked data-checked="true" */

	li[data-checked='true'] {
		> div {
			opacity: 0.5;
			text-decoration: line-through;
		}
	}

	input[type='checkbox'] {
		cursor: pointer;
	}

	ul[data-type='taskList'] {
		margin: 0;
	}

	/* Reset nested regular ul elements to default styling */
	ul:not([data-type='taskList']) {
		list-style: disc;
		padding-left: 1rem;

		li {
			align-items: initial;
			display: list-item;

			label {
				flex: initial;
				margin-right: initial;
				margin-top: initial;
				user-select: initial;
				display: initial;
			}

			div {
				flex: initial;
				align-items: initial;
			}
		}
	}
}

@media (prefers-color-scheme: dark) {
	.ProseMirror p.is-editor-empty:first-child::before {
		color: #757575;
	}
}

.ai-autocompletion::after {
	color: #a0a0a0;

	content: attr(data-suggestion);
	pointer-events: none;
}

.tiptap pre > code {
	border-radius: 0.4rem;
	font-size: 0.85rem;
	padding: 0.25em 0.3em;

	@apply dark:bg-gray-800 bg-gray-50;
}

.tiptap pre {
	border-radius: 0.5rem;
	font-family: 'JetBrainsMono', monospace;
	margin: 1.5rem 0;
	padding: 0.75rem 1rem;

	@apply dark:bg-gray-800 bg-gray-50;
}

.tiptap p code {
	color: #eb5757;
	border-width: 0px;
	padding: 3px 8px;
	font-size: 0.8em;
	font-weight: 600;
	@apply rounded-md dark:bg-gray-800 bg-gray-50 mx-0.5;
}

/* Code styling */
.hljs-comment,
.hljs-quote {
	color: #616161;
}

.hljs-variable,
.hljs-template-variable,
.hljs-attribute,
.hljs-tag,
.hljs-regexp,
.hljs-link,
.hljs-name,
.hljs-selector-id,
.hljs-selector-class {
	color: #f98181;
}

.hljs-number,
.hljs-meta,
.hljs-built_in,
.hljs-builtin-name,
.hljs-literal,
.hljs-type,
.hljs-params {
	color: #fbbc88;
}

.hljs-string,
.hljs-symbol,
.hljs-bullet {
	color: #b9f18d;
}

.hljs-title,
.hljs-section {
	color: #faf594;
}

.hljs-keyword,
.hljs-selector-tag {
	color: #70cff8;
}

.hljs-emphasis {
	font-style: italic;
}

.hljs-strong {
	font-weight: 700;
}

/* Table styling for tiptap editors */
.tiptap table {
	@apply w-full text-sm text-left text-gray-500 dark:text-gray-400 max-w-full;
}

.tiptap thead {
	@apply text-xs text-gray-700 uppercase bg-gray-50 dark:bg-gray-850 dark:text-gray-400 border-none;
}

.tiptap th,
.tiptap td {
	@apply px-3 py-1.5 border border-gray-100 dark:border-gray-850;
}

.tiptap th {
	@apply cursor-pointer text-left text-xs text-gray-700 dark:text-gray-400 font-semibold uppercase bg-gray-50 dark:bg-gray-850;
}

.tiptap td {
	@apply text-gray-900 dark:text-white w-max;
}

.tiptap tr {
	@apply bg-white dark:bg-gray-900 dark:border-gray-850 text-xs;
}

.tippy-box[data-theme~='transparent'] {
	@apply bg-transparent p-0 m-0;
}

/* this is a rough fix for the first cursor position when the first paragraph is empty */
.ProseMirror > .ProseMirror-yjs-cursor:first-child {
	margin-top: 16px;
}
/* This gives the remote user caret. The colors are automatically overwritten*/
.ProseMirror-yjs-cursor {
	position: relative;
	margin-left: -1px;
	margin-right: -1px;
	border-left: 1px solid black;
	border-right: 1px solid black;
	border-color: orange;
	word-break: normal;
	pointer-events: none;
}
/* This renders the username above the caret */
.ProseMirror-yjs-cursor > div {
	position: absolute;
	top: -1.05em;
	left: -1px;
	font-size: 13px;
	background-color: rgb(250, 129, 0);
	user-select: none;
	color: white;
	padding-left: 2px;
	padding-right: 2px;
	white-space: nowrap;
}
