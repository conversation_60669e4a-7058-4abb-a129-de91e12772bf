# DevContainer Setup Guide for Open WebUI

This guide shows how to set up a complete development environment for Open WebUI using DevContainers, starting from a clean CLI-generated base and adding development-specific configurations.

## 📋 What Was Done (CLI Phase)

The base devcontainer was created using the DevContainer CLI with **optimized approach**:

```bash
devcontainer templates apply \
  --workspace-folder . \
  --template-id ghcr.io/devcontainers/templates/python \
  --template-args '{"pythonVersion": "3.11"}' \
  --features '[
    {"id": "ghcr.io/devcontainers/features/node:1", "options": {"nodeVersion": "18"}},
    {"id": "ghcr.io/devcontainers/features/common-utils:2", "options": {"installZsh": true, "username": "vscode"}},
    {"id": "ghcr.io/devcontainers/features/git:1"},
    {"id": "ghcr.io/devcontainers/features/github-cli:1"}
  ]'
```

**Design Decisions:**
- ✅ **Python 3.11 base** (backend-first approach since Open WebUI is primarily Python)
- ✅ **Node.js 18 added as feature** (frontend support)
- ✅ **No Docker-in-Docker** (not needed for app development)
- ✅ **Minimal, focused setup** (add features as needed)

**Current Infrastructure:**
- ✅ Python 3.11 environment (primary)
- ✅ Node.js 18 for frontend development (secondary)
- ✅ Git and GitHub CLI for version control
- ✅ Zsh shell with utilities
- ✅ Port forwarding (5173, 8080)
- ✅ Dependency installation setup
- ✅ Environment variables for Open WebUI

## 🔧 Next Steps: Complete Development Setup

### Step 1: Add VS Code Extensions

Open the `.devcontainer/devcontainer.json` file and add the `customizations` section:

```json
{
  "name": "Open WebUI Development",
  "image": "mcr.microsoft.com/devcontainers/python:1-3.11-bullseye",
  
  "features": {
    // ... existing features ...
  },
  
  "forwardPorts": [5173, 8080],
  
  "postCreateCommand": "pip3 install -r backend/requirements.txt && npm install",
  
  "containerEnv": {
    "PYTHONPATH": "/workspaces/open-webui/backend",
    "NODE_ENV": "development"
  },
  
  // Add this entire section:
  "customizations": {
    "vscode": {
      "extensions": [
        // Python Development
        "ms-python.python",
        "ms-python.pylint",
        "ms-python.black-formatter",
        "ms-python.isort",
        "ms-python.flake8",
        "charliermarsh.ruff",
        
        // JavaScript/TypeScript/Svelte
        "svelte.svelte-vscode",
        "bradlc.vscode-tailwindcss",
        "esbenp.prettier-vscode",
        "dbaeumer.vscode-eslint",
        "ms-vscode.vscode-typescript-next",
        
        // General Development
        "ms-vscode.vscode-json",
        "redhat.vscode-yaml",
        "ms-vscode.makefile-tools",
        "GitHub.vscode-github-actions",
        "streetsidesoftware.code-spell-checker",
        "ms-vscode.vscode-todo-highlight",
        "PKief.material-icon-theme",
        "GitHub.copilot"
      ],
      
      "settings": {
        // Python Configuration
        "python.defaultInterpreterPath": "/usr/local/bin/python",
        "python.linting.enabled": true,
        "python.linting.pylintEnabled": true,
        "python.linting.flake8Enabled": true,
        "python.formatting.provider": "black",
        "python.formatting.blackArgs": [
          "--line-length=88",
          "--exclude=.venv/|/venv/"
        ],
        "python.sortImports.args": [
          "--profile=black"
        ],
        
        // Editor Configuration
        "editor.formatOnSave": true,
        "editor.codeActionsOnSave": {
          "source.organizeImports": true,
          "source.fixAll.eslint": true
        },
        "editor.rulers": [88],
        "files.trimTrailingWhitespace": true,
        "files.insertFinalNewline": true,
        
        // Language-specific settings
        "[python]": {
          "editor.tabSize": 4,
          "editor.insertSpaces": true,
          "editor.detectIndentation": false,
          "editor.defaultFormatter": "ms-python.black-formatter"
        },
        "[javascript]": {
          "editor.defaultFormatter": "esbenp.prettier-vscode",
          "editor.tabSize": 2
        },
        "[typescript]": {
          "editor.defaultFormatter": "esbenp.prettier-vscode",
          "editor.tabSize": 2
        },
        "[svelte]": {
          "editor.defaultFormatter": "svelte.svelte-vscode",
          "editor.tabSize": 2
        },
        "[json]": {
          "editor.defaultFormatter": "esbenp.prettier-vscode"
        },
        "[yaml]": {
          "editor.defaultFormatter": "redhat.vscode-yaml"
        },
        
        // Tailwind CSS
        "tailwindCSS.includeLanguages": {
          "svelte": "html"
        },
        "emmet.includeLanguages": {
          "svelte": "html"
        },
        
        // Terminal
        "terminal.integrated.defaultProfile.linux": "zsh"
      }
    }
  }
}
```

### Step 2: Add Development Environment Variables

Add these to the `containerEnv` section:

```json
"containerEnv": {
  "PYTHONPATH": "/workspaces/open-webui/backend",
  "NODE_ENV": "development",
  "OLLAMA_BASE_URL": "http://host.docker.internal:11434",
  "WEBUI_SECRET_KEY": "dev-secret-key-change-in-production",
  "DATABASE_URL": "sqlite:///workspaces/open-webui/backend/webui.db",
  "ENABLE_COMMUNITY_SHARING": "False",
  "ENABLE_EVAL_ARENA": "False"
}
```

### Step 3: Add Lifecycle Commands

Update the commands section:

```json
"postCreateCommand": "pip3 install -r backend/requirements.txt && npm install",
"postStartCommand": "echo 'Development environment ready! Frontend: npm run dev | Backend: cd backend && python -m uvicorn open_webui.main:app --host 0.0.0.0 --port 8080 --reload'",
"onCreateCommand": "git config --global --add safe.directory /workspaces/open-webui"
```

### Step 4: Add Development Tools Features

You can add more features using VS Code's command palette:

1. **Open Command Palette** (`Ctrl+Shift+P` / `Cmd+Shift+P`)
2. **Type:** `Dev Containers: Configure Container Features`
3. **Select features to add:**
   - `ghcr.io/devcontainers-community/features/uv:1` - UV Python package manager
   - `ghcr.io/devcontainers/features/azure-cli:1` - Azure CLI (if needed)
   - `ghcr.io/devcontainers/features/aws-cli:1` - AWS CLI (if needed)

**Note:** If you have local templates in `~/Downloads/templates-main/`, you can also use them by referencing local paths in the CLI command or copying them to your project.

### Step 5: Create Configuration Files

#### Create `.vscode/settings.json` (Project-level settings):

```json
{
  "python.defaultInterpreterPath": "/usr/local/bin/python",
  "python.terminal.activateEnvironment": false,
  "python.testing.pytestEnabled": true,
  "python.testing.pytestArgs": ["backend/tests"],
  "python.testing.unittestEnabled": false,
  "python.testing.nosetestsEnabled": false,
  
  "eslint.workingDirectories": ["."],
  "prettier.configPath": ".prettierrc",
  
  "files.associations": {
    "*.svelte": "svelte"
  },
  
  "search.exclude": {
    "**/node_modules": true,
    "**/.venv": true,
    "**/venv": true,
    "**/__pycache__": true,
    "**/.pytest_cache": true,
    "**/build": true,
    "**/dist": true
  }
}
```

#### Create `.vscode/launch.json` (Debugging configuration):

```json
{
  "version": "0.2.0",
  "configurations": [
    {
      "name": "Python: FastAPI Backend",
      "type": "python",
      "request": "launch",
      "program": "/usr/local/bin/uvicorn",
      "args": [
        "open_webui.main:app",
        "--host", "0.0.0.0",
        "--port", "8080",
        "--reload"
      ],
      "cwd": "${workspaceFolder}/backend",
      "env": {
        "PYTHONPATH": "${workspaceFolder}/backend"
      },
      "console": "integratedTerminal"
    },
    {
      "name": "Python: Debug Current File",
      "type": "python",
      "request": "launch",
      "program": "${file}",
      "console": "integratedTerminal",
      "cwd": "${workspaceFolder}/backend"
    }
  ]
}
```

#### Create `.vscode/tasks.json` (Build tasks):

```json
{
  "version": "2.0.0",
  "tasks": [
    {
      "label": "Start Frontend",
      "type": "shell",
      "command": "npm run dev",
      "group": "build",
      "presentation": {
        "echo": true,
        "reveal": "always",
        "focus": false,
        "panel": "new"
      },
      "runOptions": {
        "runOn": "folderOpen"
      }
    },
    {
      "label": "Start Backend",
      "type": "shell",
      "command": "cd backend && python -m uvicorn open_webui.main:app --host 0.0.0.0 --port 8080 --reload",
      "group": "build",
      "presentation": {
        "echo": true,
        "reveal": "always",
        "focus": false,
        "panel": "new"
      }
    },
    {
      "label": "Run Tests",
      "type": "shell",
      "command": "cd backend && python -m pytest",
      "group": "test",
      "presentation": {
        "echo": true,
        "reveal": "always",
        "focus": false,
        "panel": "new"
      }
    },
    {
      "label": "Format Code",
      "type": "shell",
      "command": "npm run format && npm run format:backend",
      "group": "build",
      "presentation": {
        "echo": true,
        "reveal": "always",
        "focus": false,
        "panel": "new"
      }
    }
  ]
}
```

### Step 6: Create Development Scripts

#### Create `scripts/dev-setup.sh`:

```bash
#!/bin/bash

# Install backend dependencies
echo "Installing backend dependencies..."
pip3 install -r backend/requirements.txt

# Install frontend dependencies
echo "Installing frontend dependencies..."
npm install

# Setup git hooks (optional)
echo "Setting up git..."
git config --global user.email "<EMAIL>" || true
git config --global user.name "Developer" || true

# Create necessary directories
mkdir -p backend/logs
mkdir -p backend/uploads

echo "Setup complete! 🚀"
echo "To start development:"
echo "  Frontend: npm run dev"
echo "  Backend: cd backend && python -m uvicorn open_webui.main:app --host 0.0.0.0 --port 8080 --reload"
```

### Step 7: Optional Advanced Features

#### Add Database Management:

```json
{
  "features": {
    // ... existing features ...
    "ghcr.io/devcontainers/features/docker-compose:2": {},
    "ghcr.io/devcontainers-community/features/postgres:1": {
      "version": "13"
    }
  }
}
```

#### Add AI/ML Development Tools:

```json
{
  "features": {
    // ... existing features ...
    "ghcr.io/devcontainers/features/nvidia-cuda:1": {
      "version": "11.8"
    }
  }
}
```

## 🚀 Usage Instructions

### Building the Container

1. **Open in VS Code:**
   ```bash
   code .
   ```

2. **Open in Container:**
   - Press `Ctrl+Shift+P` / `Cmd+Shift+P`
   - Type: `Dev Containers: Open Folder in Container`
   - Select the project folder

3. **Wait for Setup:**
   - The container will build and install dependencies
   - Extensions will be installed automatically

### Development Workflow

#### Starting Development Servers:

```bash
# Terminal 1: Frontend
npm run dev

# Terminal 2: Backend
cd backend && python -m uvicorn open_webui.main:app --host 0.0.0.0 --port 8080 --reload
```

#### Running Tests:

```bash
# Frontend tests
npm test

# Backend tests
cd backend && python -m pytest

# Linting
npm run lint
```

#### Formatting Code:

```bash
# Format all code
npm run format && npm run format:backend

# Or use VS Code's format on save feature
```

## 🔧 Troubleshooting

### Common Issues:

1. **Port conflicts:** Change ports in `forwardPorts` if needed
2. **Permission issues:** Ensure container user has proper permissions
3. **Extension not working:** Reload window or rebuild container
4. **Python path issues:** Check `PYTHONPATH` environment variable

### Debugging Steps:

1. **Check container logs:**
   ```bash
   devcontainer logs
   ```

2. **Rebuild container:**
   - Command Palette → `Dev Containers: Rebuild Container`

3. **Check feature installation:**
   ```bash
   # Inside container
   which python3
   which node
   which git
   ```

## 📚 Additional Resources

- [DevContainers Documentation](https://containers.dev/)
- [VS Code DevContainer Extension](https://marketplace.visualstudio.com/items?itemName=ms-vscode-remote.remote-containers)
- [Available Features](https://containers.dev/features)
- [Open WebUI Documentation](https://github.com/open-webui/open-webui)

## 🎉 You're All Set!

Your DevContainer environment is now ready for Open WebUI development with:
- ✅ Full Python + Node.js development stack
- ✅ Linting and formatting tools
- ✅ Debugging capabilities
- ✅ Testing framework
- ✅ Git integration
- ✅ VS Code extensions and settings
- ✅ Development scripts and tasks

Happy coding! 🚀
